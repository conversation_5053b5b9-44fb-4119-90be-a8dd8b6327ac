/*-------------------------------------------
    Preloader Area Start
-------------------------------------------*/
#preloader {
    background-color: #fff;
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 9999999999999999;
}

#preloader_status, #inner-status {
    width: 100%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    height: 100%;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
}
/*-------------------------------------------
    Preloader Area End
-------------------------------------------*/

/* For Customizing Admin Dashboard Start */
.color-green {
    color: #4CBF4C;
}
/* For Customizing Admin Dashboard End */

/* Default Reset CSS from Frontend to Admin Dashboard Panel */
.font-16 {
    font-size: 16px;
}
.font-14{
    font-size: 14px;
}
.font-15 {
    font-size: 15px;
}
.font-medium {
    font-weight: 500;
}
.radius-4 {
    border-radius: 4px;
}
.p-20 {
    padding: 20px;
}
.p-30 {
    padding: 30px;
}
.radius-8 {
    border-radius: 8px;
}
.radius-3 {
    border-radius: 3px;
}
.color-heading {

}
.bg-deep-orange {
    background-color: #FF3C16;
}
.placeholder-color{
    color: #a0a3aa;
}
.form-control:focus {
    color: #212529;
    box-shadow: none;
}
.form-control {
    border: 1px solid #DFE3EB;
    font-weight: 400;
    font-size: 14px;
    line-height: 17px;
    color: #596680;
    background: #FAFBFC;
    box-sizing: border-box;
    border-radius: 8px;
    padding: 11px 16px 12px;
}
label {
    margin-bottom: 11px;
    text-transform: capitalize;
    font-weight: 500;
    font-size: 14px;
    line-height: 17px;
    color: #273041;
}
.upload-img-box-icon {
    background-color: rgba(255,255,255,.3);
    padding: 5px 15px;
    border-radius: 4px;
}
.btn-check:active+.btn-danger:focus, .btn-check:checked+.btn-danger:focus, .btn-danger.active:focus, .btn-danger:active:focus, .show>.btn-danger.dropdown-toggle:focus {
    box-shadow: none;
}
.dropdown-item.active, .dropdown-item:active {
    /* color: #fff;
    text-decoration: none; */
    background-color: #e9ecef;
}

/* Custom Scrollbar start */
.custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: #E6E3EB #fff;
}
.custom-scrollbar::-webkit-scrollbar {
    width: 4px;
    height: 10px;
}
.custom-scrollbar::-webkit-scrollbar-track {
    background-color: #fff;
}
.custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: #E6E3EB;
}
.custom-scrollbar::-webkit-scrollbar-track, .custom-scrollbar::-webkit-scrollbar-thumb {
    border-radius: 5px;
}
/* Custom Scrollbar end */

/* Default Reset CSS from Frontend to Admin Dashboard Panel */

/*-------------------------------------------
    Sweet Alert Area Page Start
-------------------------------------------*/
.swal2-icon {
    width: 2.75em!important;
    height: 2.75em!important;
}
.swal2-icon .swal2-icon-content {
    font-size: 1.75em!important;
}
.swal2-styled.swal2-confirm {
    background-color: #4D88FF!important;
}
.swal2-title {
    font-size: 1.5em!important;
    color: #4D88FF!important;
    font-weight: 500!important;
}
.swal2-styled {
    padding: .500em 1em!important;
    box-shadow: none!important;
}

/*Error Alert*/
.swal2-icon.swal2-error [class^=swal2-x-mark-line] {
    top: 2.3125em!important;
    width: 2.5em!important;
}
.swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=left] {
    left: 3px!important;
    top: 19px!important;
}
.swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=right] {
    right: 3px!important;
    top: 19px!important;
}
/*Error Alert*/

/*-------------------------------------------
    Sweet Alert Area Page End
-------------------------------------------*/
/*-------------------------------------------
    Cookie Concent Area Page Start
-------------------------------------------*/
.cookie-consent {
    position: fixed;
    bottom: 5px;
    left: 12px;
    width: 296px;
    padding-top: 7px;
    min-height: 140px;
    color: #000!important;
    line-height: 26px;
    padding-left: 23px;
    padding-right: 33px;
    font-size: 16px;
    background: #f4f4f4;
    z-index: 9999;
    cursor: pointer;
    border-radius: 3px;
    border: 1px solid #e9e9e9;
    display: flex;
    flex-direction: column;
    justify-content: center;
}
.main-cookie-content .primary-btn {
    display: inline-block;
    padding: .3rem 1.1rem;
    text-transform: capitalize;
    color: #fff;
    background-color: #4D88FF;
    -webkit-transition: all .3s linear;
    transition: all .3s linear;
    border: none;
    border-radius: 5px;
    margin-top: 10px;
}
.front-close-btn.btn-close {
    top: 3px;
    right: 3px;
    font-size: 10px;
    padding: 6px;
}
/*-------------------------------------------
    Cookie Concent Area Page End
-------------------------------------------*/

/* Admin Dashboard Support Ticket Replies Page */
    /* Ticket Details Page */
    .ticket-details-box {
        border: 1px solid #EBEBEB;
    }
    .ticket-replies-wrap .ticket-reply-item:not(:last-child) {
        margin-bottom: 15px;
    }
    .ticket-reply-item {padding: 20px;border-radius: 4px;}

    .ticket-reply-item.ticket-reply-item-student {background-color: #F5F5F5;}

    .ticket-reply-item.ticket-reply-item-staff {background-color: rgba(117, 79, 254, 0.05);}
    .ticket-reply-content p, .ticket-info-content p {
        font-size: 15px;
        line-height: 22px;
    }

    /* ticket-details-reply-form-box */
    .ticket-details-reply-form-box .upload-img-btn-wrapper button {
        color: #fff!important;
    }
    .ticket-status-box {
        padding: 4px 16px;
        margin-left: 10px;
    }
    .ticket-info-content p {
        margin: 8px 0 0;
    }
    .ticket-info-content {
        margin: 20px 0 0;
    }
    .ticket-info-bottom-box {
        border-top: 1px solid #E5E8EC;
        margin-top: 20px;
    }
    /* Ticket Details Page */

    /* Extra custom for Ticket details */
    .ticket-details-left-part textarea {
        border: 1px solid rgba(0, 0, 0, 0.07);
    }

    @media only screen and (max-width:400px) {
        .ticket-details-box-title {
            flex-direction: column;
            align-items: flex-start!important;
        }
        .ticket-info-right-status {
            margin-top: 15px;
        }
    }
    /* Extra custom for Ticket details */

/* Admin Dashboard Support Ticket Replies Page */

/* Manage Course Menu Pages */
.approve-btn {
    background-color: #4CBF4C;
    padding: 2px 7px;
    border-radius: 5px;
    align-items: center;
    color: #fff;
    line-height: 26px;
    transition: all ease .6s;
    min-width: 54px;
    text-align: center;
}
.approve-btn:hover {
    background-color: #19a719;
    color: #fff;
}
.hold-btn {
    background-color: #FFAA00;
    padding: 2px 7px;
    border-radius: 5px;
    align-items: center;
    color: #fff;
    line-height: 26px;
    transition: all ease .6s;
    min-width: 54px;
    text-align: center;
}
.hold-btn:hover {
    background-color: #e99c01;
    color: #fff;
}
/* Manage Course Menu Pages */

/* Manage Coupon Menu Pages */
.select2-container--default .select2-selection--multiple {
    border: 1px solid #DFE3EB!important;
    padding: 5px 16px 5px;
}
.select2-container--default.select2-container--focus .select2-selection--multiple {
    border: 1px solid #DFE3EB!important;
}
/* Manage Coupon Menu Pages */

/* Manage Blog Menu Pages */
.admin-dashboard-blog-list-img img {
    width: 80px;
}
.select2-container--open .select2-dropdown--above {
    max-width: 100%!important;
}
/* Manage Blog Menu Pages */

/* Manage Language Menu Pages */
.admin-dashboard-translate-your-language-page .input__group {
    flex-direction: row;
}
.admin-dashboard-translate-your-language-page .input__group label {
    text-align: left!important;
}
/* Manage Language Menu Pages */

/* Admin Dashboard inner image height */
.upload-img-box {
    height: 200px;
    width: 200px;
    max-width: 100%;
}
.upload-img-box img {
    height: 200px;
}
/* Admin Dashboard inner image height */

/* Admin General Settings */
.admin-general-settings-page .input__group {
    justify-content: flex-start;
}
.admin-general-settings-page .upload-img-box {
    height: 150px;
    max-width: 200px;
}
.admin-general-settings-page .upload-img-box img {
    height: 150px;
}
.input__group.general-settings-btn {
    justify-content: flex-end;
}

.admin-general-settings-page .input__group {
    /* justify-content: flex-start!important; */
}

/* Meta */
.admin-dashboard-meta-settings .input__group {
    align-items: flex-start;
}
.admin-dashboard-meta-settings .input__group {
    justify-content: flex-end;
}
/* Meta */

/* Zoom Meeting */
.email-inbox__area .input__group {
    flex-direction: row;
}
/* Zoom Meeting */

/* Special Feature Section */
.admin-special-feature-section-page .upload-img-box {
    height: 150px;
    width: 150px;
}
/* Special Feature Section */

/* Become Instructor Section */

.admin-become-instructor-video-section .upload-img-box {
    height: 150px;
}
/* Become Instructor Section */

/* Mail Configuration Section */

@media only screen and (max-width:480px) {
    .item-top {
        flex-direction: column;
        align-items: flex-start;
    }
    .item-title {
        flex-direction: column;
        align-items: flex-start;
    }
}
.item-title > h2 {
    margin-bottom: 10px;
}
/* Mail Configuration Section */

/* Payment Method Section */
.admin-dashboard-payment-title-right, .admin-dashboard-payment-content-box-right {
    border-left: none!important;
}

@media only screen and (max-width:766px) {
    .admin-dashboard-payment-content-box-right {
        border-left: 1px solid #dee2e6!important;
        border-top: none!important;
    }
}
/* Payment Method Section */

/* Insturctor Feature Setting */
.btn-icon-remove {
    width: 44px;
    height: 44px;
}
.admin-instructor-feature-settings .upload-img-box,
.admin-instructor-procedure-settings .upload-img-box,
.admin-team-member-section .upload-img-box,
.admin-about-us-instructor-support .upload-img-box,
.admin-about-us-client .upload-img-box {
    height: 150px;
    width: 150px;
}
.admin-instructor-feature-settings .upload-img-box img,
.admin-instructor-procedure-settings .upload-img-box img,
.admin-team-member-section .upload-img-box img,
.admin-about-us-instructor-support .upload-img-box img,
.admin-about-us-client .upload-img-box img {
    height: 150px;
}

@media only screen and (max-width:991px) {
    .removeClass > label.opacity-0 {
        display: none;
    }
}
/* Insturctor Feature Setting */

/* Admin Menu Notification */
.admin-notification-menu .badge {
    background: #4D88FF!important;
    width: 16px;
    height: 16px;
    top: 5px!important;
    left: 15px!important;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 8px;
    color: #fff;
}
.admin-notification-menu .dropdown-list {
    max-height: 240px;
    overflow-y: auto;
    width: 230px;
}
.admin-notification-menu .message-user-item {
    /* flex-wrap: wrap; */
    border-top: 1px solid #F0F0F0;
    margin-top: 10px;
    padding: .5rem 16px!important;
    white-space: normal;
    font-size: 15px;
    color: var(--para-color);
    justify-content: space-between;
}
/* .admin-notification-menu .dropdown-menu li:last-child .message-user-item {
    border-bottom: none;
} */
.admin-notification-menu .message-user-item.active, .admin-notification-menu .message-user-item:hover {
    /* background-color: #F8F5FA!important; */
}
.admin-notification-menu .dropdown-menu li:first-child .message-user-item {
    border-top: none!important;
    margin-top: 0!important;
}
.admin-notification-menu .message-user-item .user-img-wrap, .admin-notification-menu .message-user-item .user-img-wrap img {
    height: 40px;
    width: 40px;
    border-radius: 50%;
    overflow: hidden;
}
.message-user-item-left p {
    line-height: 16px;
}
.radius-50 {
    border-radius: 50%;
}
.color-heading {
    color: #040453;
}
.font-14 {
    font-size: 14px;
}
.font-13 {
    font-size: 13px;
}
.font-11 {
    font-size: 11px;
}
.color-gray {
    color: #767588;
}
/* Admin Menu Notification */

/* Admin Menu Sidebar Icon Change */
.sidebar__menu li a .iconify {
    font-size: 20px;
    width: 20px;
    height: 20px;
}
/* Admin Menu Sidebar Icon Change */

/* Manage Finance Menu */
.status.active {
    display: inline-block;
    text-align: center;
}
.rejected-note-box, .complete-withdrawal-note-box {
    min-width: 220px!important;
}
/* Manage Finance Menu */

/* Admin General Settings */

/* Admin Manage Course View Course */
.admin-course-watch-page-area .accordion-item {
    background-color: transparent;
}
.admin-course-watch-page-area .accordion-header {
    margin-bottom: 0;
}
.admin-course-watch-page-area .accordion-button:not(.collapsed), .admin-course-watch-page-area .accordion-button:hover, .admin-course-watch-page-area.accordion-header {
    color: #040453;
    background-color: transparent;
}
.admin-course-watch-page-area .curriculum-content .accordion-button {
    background-color: transparent;
    line-height: 19px;
    font-size: 16px;
    padding: 20px;
}
.admin-course-watch-page-area .accordion-button:focus, .admin-course-watch-page-area .accordion-button:not(.collapsed) {
    box-shadow: none;
}
.admin-course-watch-page-area .accordion-body {
    padding: 0 1rem 1.25rem;
}
.admin-course-watch-page-area .curriculum-content .accordion-button::after {
    content: "\e92e";
}
.admin-course-watch-page-area .accordion-button:not(.collapsed)::after {
    background-image: none;
}
.admin-course-watch-page-area .accordion-button::after {
    flex-shrink: 0;
    width: 1.25rem;
    height: 1.25rem;
    margin-left: auto;
    content: "\e92e";
    transition: transform .3s ease-in-out;
    border-left: none;
    font-family: Feather!important;
    background-image: none;
    background-repeat: no-repeat;
    background-size: 0.625rem 0.625rem;
    float: right;
}
.admin-course-watch-page-area .play-list-item {
    margin-bottom: 15px;
}
.admin-course-watch-page-area .play-list-item.venobox, .admin-course-watch-page-area .play-list-item.preview-enabled {
    cursor: pointer;
}
.admin-course-watch-page-area .play-list-item.venobox:hover .play-list-left p, .admin-course-watch-page-area .play-list-item.preview-enabled:hover .play-list-left p, .admin-course-watch-page-area .play-list-item:hover .show-preview {
    color: #5e3fd7;
}
.admin-course-watch-page-area .play-list-item.venobox .play-list-left p, .admin-course-watch-page-area .play-list-item.preview-enabled .play-list-left p, .admin-course-watch-page-area .show-preview {
    color: #040453;
    text-decoration: underline;
    transition: .5s ease all;
}
.admin-course-watch-page-area .play-list-left p {
    padding-top: 2px;
    margin-bottom: 0;
}
.admin-course-watch-page-area .play-list-left img {
    margin-right: 18px;
    height: 18px;
}
.admin-course-watch-page-area .play-list-item.preview-enabled .show-preview {
    display: block;
}
.admin-course-watch-page-area .play-list-item.venobox .play-list-left p, .admin-course-watch-page-area .play-list-item.preview-enabled .play-list-left p, .admin-course-watch-page-area .show-preview {
    color: #040453;
    text-decoration: underline;
    transition: .5s ease all;
    margin-bottom: 0;
}
.admin-course-watch-page-area .show-preview {
    display: none;
    margin-right: 30px;
}
.font-18 {
    font-size: 18px;
    line-height: 19px;
}
.font-medium {
    font-weight: 500;
}
/* Admin Manage Course View Course */

/*Order Report Table*/
.admin-order-payment-method {
    min-width: 177px!important;
}
/*Order Report Table*/

/*Summernote custom height*/
.note-editor.note-airframe .note-editing-area .note-editable, .note-editor.note-frame .note-editing-area .note-editable {
    height: 300px;
}
/*Summernote custom height*/

/*------------------------------------
  Summer Note Area Start
--------------------------------------*/
.note-modal-footer {
    height: auto;
    padding: 8px 10px 25px;
}
.note-modal-footer .note-btn {
    float: initial;
}
.btn-fullscreen {
    display: none!important;
}
/*------------------------------------
  Summer Note Area End
--------------------------------------*/

/*------------------------------------
  Affiliate Pages Area Start
--------------------------------------*/

/* Affiliate List Page */

/* Affiliate Tab Navlist */
.affiliate-tab-list-top {
    border-bottom: 1px solid #ECEDF0;
    margin-bottom: 30px;
}
.affiliate-tab-list-top .nav-item {
    margin-right: 25px;
}
.affiliate-tab-list-top .nav-link {
    color: #596680;
    font-weight: 500;
    padding: .75rem 0;
    border-top: none;
    border-left: none;
    border-right: none;
    border-bottom: 2px solid transparent;
}
.affiliate-tab-list .nav-link:focus, .affiliate-tab-list .nav-link:hover {
    border-color: transparent;
}
.affiliate-tab-list-top .nav-item.show .nav-link, .affiliate-tab-list-top .nav-link.active {
    border-bottom: 2px solid #4C88FF;
    color: #273041;
}
/* Affiliate Tab Navlist */

/* Affiliate Table Start */
.affiliate-table table .dropdown-menu  {
    background: #FFFFFF;
    border: 1px solid #E5E8EC;
    box-shadow: -6px 6px 20px rgba(176, 176, 176, 0.25);
    border-radius: 6px;
}
.affiliate-table table .dropdown-menu .dropdown-item {
    font-size: 14px;
    line-height: 17px;
    color: #52526C;
    padding: .5rem 1rem;
}
/* Affiliate Table End */

/* Affiliate List Page */

/* Affiliate History Page Start */
.affiliate-history-filter-wrap {
    flex-direction: row;
    flex-wrap: wrap;
    position: absolute;
    right: 0;
    z-index: 9;
    margin-bottom: 25px;
}

@media only screen and (max-width: 991.98px) {
    .affiliate-history-filter-wrap {
        position: relative;
    }
    .affiliate-filter-history-item {
        margin-bottom: 10px;
        margin-left: 0!important;
        margin-right: 1rem;
    }
}
/* Affiliate History Page End */

/* Affiliate Payouts Page Start */
.payouts-tab-content .dataTables_filter {
    display: none;
}
.affiliate-table .data-table-filter td {
    padding: 33px 20px 33px!important;
}
/* Affiliate Payouts Page End */

/* Datatables CSS Start */
table.dataTable thead th {
    min-width: 100px;
}
/* Datatables CSS End */

/*------------------------------------
  Affiliate Pages Area End
--------------------------------------*/


.status.pending {
    background-color: #00bfff;
    color: #000;
    display: inline-block;
    text-align: center;
}

.status.upcoming {
    background-color: #ffc400;
    color: #000;
    display: inline-block;
    text-align: center;
}