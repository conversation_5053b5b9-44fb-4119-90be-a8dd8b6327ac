<?php

namespace App\Http\Controllers;

use App\Models\Batch;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Traits\ToastrMessage;
use Illuminate\Support\Facades\Validator;

class BatchController extends Controller
{
    use ToastrMessage;

    public function index(Request $request)
    {
        $filter = $request->search_name ?? '';

        $batches = Batch::where('instructor_id', auth()->user()->id)
        ->when($filter, function ($query, $filter) {
            return $query->where('title', 'like', '%'.$filter.'%');
        })
        ->orderBy('id', 'desc')
        ->paginate(10);
        return view('instructor.batches.index', compact('batches'));
    }

    public function create(){
      return view('instructor.batches.create');  
    }

    public function store(Request $request){

        $validator = Validator::make($request->all(), [
            'title' => 'required',
            'status' => 'required',
        ]);
        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }
        $subject = Batch::create([
            'title'  => $request->title,
            'status' => $request->status,
            'code'   =>'BATCH-'.(string) Str::uuid(),
            'instructor_id' => auth()->id()
        ]);
        $this->success(__('Batch successfully Created'));
        return redirect()->back();
    }

    public function edit($id){
        $batch = Batch::where('id',$id)->where('instructor_id', auth()->user()->id)->first();
        return view('instructor.batches.edit',compact('batch'));  
    }

    public function update(Request $request){

        $validator = Validator::make($request->all(), [
            'title' => 'required',
            'id' => 'required',
            'status' => 'required'
        ]);
        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        Batch::where('id',$request->id)->update([
            'title' => $request->title,
            'status' => $request->status,
            'instructor_id' => auth()->id()
        ]);

        $this->success(__('Batch successfully Updated'));
        return redirect()->route('common.exam.batch.index');

    }

    public function delete($id){

        Batch::where('id',$id)->where('instructor_id', auth()->user()->id)->delete();
        $this->success(__('Batch successfully deleted'));
        return redirect()->route('common.exam.batch.index');
    }

    public function getBatchList(Request $request){

        try{
            $batches = Batch::where('status',1)->with('instructor:id,name')->get();
            $batches = view('frontend.student.exam_center.content.get_batch_list', compact('batches'))->render();
            return response()->json([
                'success' => true,
                'message' => 'Batch List',
                'status' => 200,
                'data' => $batches
            ]);
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => 'Something went wrong',
                'status' => 500,
                'data' => []
            ]);
        }
     
    }
}
