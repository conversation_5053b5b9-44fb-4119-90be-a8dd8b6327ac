<?php

namespace App\Http\Controllers;
use App\Models\Unit;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;

class UnitController extends Controller
{
    public function index($id)
    {
        $units = DB::table('units')->where('institute_id',$id)->latest()->paginate(10);
        return view('admin.institute.unit.view', compact('units','id'));
    }

    public function unitStore(Request $request)
    {
        $data = $request->validate([
            'title' => 'required',
            'institute_id' => 'required',
        ]);
        Unit::create($data);
        $this->showToastrMessageForSubject('success', __('Unit created successfully'));
        return redirect()->route('admin.unit.index',['id'=>$request->institute_id]);
    }

    public function edit($id)
    {   
        $unit = Unit::where('id',$id)->first();
        return response()->json($unit);
    }

    public function update(Request $request, Unit $institute)
    {
        $request->validate([
            'title' => 'required',
        ]);
        DB::table('units')->where('id',$request->unit_id)->update(['title' => $request->title]);
        $this->showToastrMessageForSubject('success', __('Unit updated successfully'));
        return redirect()->back();
    }

    public function showToastrMessageForSubject($type, $message)
    {
        switch ($type) {
            case 'success':
                return toastr()->success($message, '', ["positionClass" => "toast-bottom-right"]);
                break;
            case 'warning':
                return toastr()->warning($message, '', ["positionClass" => "toast-bottom-right"]);
                break;
            case 'error':
                return toastr()->error($message, '', ["positionClass" => "toast-bottom-right"]);
                break;
            default:
                return toastr()->success($message, '', ["positionClass" => "toast-bottom-right"]);
        }
    } 
}
