<?php

namespace App\Http\Controllers;
use App\Models\Unit;
use App\Models\Topic;
use App\Models\Chapter;
use App\Models\Subject;
use App\Models\Question;
use App\Models\Institute;
use App\Models\McqQuestion;
use Illuminate\Http\Request;
use App\Models\AcademyQuestion;
use App\Models\QuestionHistory;
use Faker\Factory as FakerFactory;
use Illuminate\Support\Facades\DB;
use App\Models\QuestionsBoardHistory;
use Illuminate\Support\Facades\Cache;

class AcademyQuestionController extends Controller
{
    private function reformateDom($domData,$chapter_id){
        if($domData==null){
            return null;
        }
        libxml_use_internal_errors(true);
        $dom = new \domdocument();
        $dom->loadHtml(mb_convert_encoding($domData, 'HTML-ENTITIES', 'UTF-8'), LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);
        $images = $dom->getelementsbytagname('img');
        foreach($images as $k => $img){
            $data = $img->getattribute('src');
            if(count(explode(';', $data))>1){
                list($type, $data) = explode(';', $data);
                list($a,$data)      = explode(',', $data);
                $data = base64_decode($data);
                $image_name= time().$k.'.png';
                $path = public_path() .'/images/chapters/'.$chapter_id."/". $image_name;
                
                if (!is_dir(public_path() .'/images/chapters/'.$chapter_id)) {
                    // dir doesn't exist, make it
                    \mkdir(public_path() .'/images/chapters/'.$chapter_id);
                  }
                file_put_contents($path, $data);
          
                $img->removeattribute('src');
                $img->setattribute('src', '/images/chapters/'.$chapter_id."/".$image_name);
            }
        }
        return $dom->savehtml();
    }

    public function all(Request $request){
        
        $query = DB::table('academy_questions')
                        ->select('academy_questions.id','academy_questions.question_type_id','academy_questions.question_text','academy_questions.academy_type',
                        'academy_questions.question_type_id','subjects.title as subject_name','chapters.title as chapter_name','topics.title as topic_name','academy_types.title as academy_type_name',
                        'question_types.title as question_type','academy_questions.created_at')
                        ->leftJoin('subjects','academy_questions.subject_id','subjects.id')
                        ->leftJoin('chapters','academy_questions.chapter_id','chapters.id')
                        ->leftJoin('topics','academy_questions.topic_id','topics.id')
                        ->leftJoin('academy_types','academy_questions.academy_type','academy_types.id')
                        ->leftJoin('question_types','academy_questions.question_type_id','question_types.id')
                        ->latest()
                        ->where('academy_questions.created_by',auth()->id());

        if($request->search_name != null){
            $questions =  $query->where('question_text', 'like', '%' . $request->search_name . '%')->paginate(10);
        }else{
            $questions = $query->paginate(15);
        }

        return view("admin.question.view",compact('questions'));
    }

    public function create(){

        $academy_types = DB::table('academy_types')->where('status',1)->select('id','title')->get()->toArray();
        $question_types = DB::table('question_types')->get()->toArray();
        $institutes = DB::table('institutes')->latest()->get();
        $cachedData = Cache::get('latest_question_info_' . auth()->id());
        return view("admin.question.create",compact('academy_types','question_types','institutes','cachedData'));

    }

    public function getSubjects($academyType){

        $subjects = Subject::where('academy_type',$academyType)->where('created_by',auth()->id())->where('status',1)->get();
        return response()->json($subjects);
    }
    
    public function getChapters($subjectId){

        $chapters = Chapter::where('subject_id',$subjectId)->where('created_by',auth()->id())->where('status',1)->get();
        return response()->json($chapters);

    }
    
    public function getTopics($chapterId){

        $topics = Topic::where('chapter_id',$chapterId)->where('created_by',auth()->id())->where('status',1)->get();
        return response()->json($topics);

    }

    public function getUnits($intsId){

        $units = Unit::where('institute_id',$intsId)->get();
        return response()->json($units);

    } 

    public function store(Request $request){

        $board_name = $request->board_name;
        $board_year = $request->board_year;
        $institute_id = $request->institute_id;
        $unit_id = $request->unit_id;
        $years = $request->years;
        $academy_type = $request->academy_type ? $request->academy_type : null ;
        if(count($institute_id) != count($years)){
            return redirect()->back()->with('error', 'Invalid Question History!');
        }
        if(count($board_name) != count($board_year)){
            return redirect()->back()->with('error', 'Invalid Question History!');
        }
        $history_count=count($institute_id);
        $chapter_id=$request->chapter_id;

        $validationRules = [
            'question_text' => ['required', 'string'],
        ];
        
        if (in_array($request->question_type_id, [1, 4])) {
            $options = range(1, ($request->question_type_id == 1) ? 4 : 5);
            foreach ($options as $option) {
                $validationRules["option_$option"] = ['required', 'string'];
            }
        } else {
            $validationRules['academy_type'] = ['required'];
        }
        
        $request->validate($validationRules);

        $question_id = AcademyQuestion::create([
            'topic_id'=>$request->topic_id,
            'chapter_id'=>$chapter_id,
            'subject_id' => $request->subject_id,
            'question_type_id'=>$request->question_type_id,
            'difficulty_level'=>$request->difficulty_level,
            'reference_id'=>$request->reference_id,
            'marks'=>$request->marks,
            'hint'=>$request->hint,
            'question_text'=>$request->question_text,
            'question_detail'=>$request->question_detail,
            'solution'=>$request->solution,
            'question_text'=>$this->reformateDom($request->question_text,$chapter_id),
            'question_detail'=>$this->reformateDom($request->question_detail,$chapter_id),
            'solution'=>$this->reformateDom($request->solution,$chapter_id),
            'academy_type' =>$academy_type,
            'created_by' => auth()->user()->id,
        ])->id;
        if($request->question_type_id == 1 || $request->question_type_id == 4){
            $data = [
                'question_id' => $question_id,
                'option_1' => $this->reformateDom($request->option_1 ?? null, $chapter_id),
                'option_2' => $this->reformateDom($request->option_2 ?? null, $chapter_id),
                'option_3' => $this->reformateDom($request->option_3 ?? null, $chapter_id),
                'option_4' => $this->reformateDom($request->option_4 ?? null, $chapter_id),
                'option_5' => $this->reformateDom($request->option_5 ?? null, $chapter_id),
                'correct_option' => $request->correct_option,
            ];

            // Create the McqQuestion
            McqQuestion::create($data);            
        }
        for($i=0;$i<$history_count;$i++){
            if($institute_id[$i]!=null && $years[$i]!=null){
                QuestionHistory::create([
                    'question_id'=>$question_id,
                    'institute_id'=>$institute_id[$i],
                    'unit_id'=>$unit_id[$i],
                    'years'=>$years[$i],
                ]);
            }
        }
        for( $y=0; $y<count($board_name); $y++){
            if($board_name[$y]!= null && $board_year[$y]!= null){
                QuestionsBoardHistory::create([
                    'question_id'=>$question_id,
                    'board_name'=>$board_name[$y],
                    'year'=>$board_year[$y],
                ]);
            }
        }
        $this->cacheLatestQuestionInfo($request->all());// Cache latest question info
        $this->showToastrMessageForSubject('success', __('Question saved successfully!'));
        return redirect()->route('instructor.academy_question.index');
    }

    public function edit($id){

        $question = AcademyQuestion::with([
            'subject',
            'topic:id,title',
            'chapter:id,title',
            'academyType:id,title',
            'questionType:id,title',
            'mcqQuestion:question_id,option_1,option_2,option_3,option_4,option_5,correct_option',
            'questionHistorie',
            'questionsBoardHistory:question_id,id,board_name,year'
        ])->where('id',$id)->where('academy_questions.created_by',auth()->id())->first();

        $academy_types = DB::table('academy_types')->where('status', 1)->select('id', 'title')->get()->toArray();
        $question_types = DB::table('question_types')->get()->toArray();
        $institutes = DB::table('institutes')->latest()->get();
        $subjects = Subject::where('academy_type',$question->academy_type)->where('created_by',auth()->id())->where('status',1)->get();
        $chapters = Chapter::where('subject_id',$question->subject_id)->where('created_by',auth()->id())->where('status',1)->get();
        $topics = Topic::where('chapter_id',$question->chapter_id)->where('created_by',auth()->id())->where('status',1)->get();
        $units = Unit::get();

        return view("admin.question.edit",compact('question' ,'academy_types', 'question_types', 'institutes', 'subjects', 'chapters','topics','units'));

    }

    public function update(Request $request ,$id){

        $academy_type = $request->academy_type ? $request->academy_type : null;
        $chapter_id = $request->chapter_id;
        $academyQuestion = [
            'topic_id'=>$request->topic_id,
            'chapter_id' => $chapter_id,
            'subject_id' => $request->subject_id,
            'question_type_id' => $request->question_type_id,
            'difficulty_level' => $request->difficulty_level,
            'reference_id' => $request->reference_id,
            'marks' => $request->marks,
            'hint' => $request->hint,
            'question_text' => $request->question_text,
            'question_detail' => $request->question_detail,
            'solution' => $request->solution,
            'question_text' => $this->reformateDom($request->question_text, $chapter_id),
            'question_detail' => $this->reformateDom($request->question_detail, $chapter_id),
            'solution' => $this->reformateDom($request->solution, $chapter_id),
            'academy_type' => $academy_type,
        ];

        $validationRules = [
            'question_text' => ['required', 'string'],
        ];
        
        if (in_array($request->question_type_id, [1, 4])) {
            $options = range(1, ($request->question_type_id == 1) ? 4 : 5);
            foreach ($options as $option) {
                $validationRules["option_$option"] = ['required', 'string'];
            }
        } else {
            $validationRules['academy_type'] = ['required'];
        }
        
        AcademyQuestion::where('id',$id)->update($academyQuestion);
        if($request->question_type_id == 1 || $request->question_type_id == 4){
            $this->updateMcqQuestion($request,$chapter_id,$id);
        }
       
        $this->updateQuestionHistorys($request, $id);

        $this->updateQuestionsBoardHistorys($request,$id);
   
        $this->showToastrMessageForSubject('success', __('Question update successfully!'));
        return redirect()->route('instructor.academy_question.index');
    }

    public function showToastrMessageForSubject($type, $message)
    {
        switch ($type) {
            case 'success':
                return toastr()->success($message, '', ["positionClass" => "toast-bottom-right"]);
                break;
            case 'warning':
                return toastr()->warning($message, '', ["positionClass" => "toast-bottom-right"]);
                break;
            case 'error':
                return toastr()->error($message, '', ["positionClass" => "toast-bottom-right"]);
                break;
            default:
                return toastr()->success($message, '', ["positionClass" => "toast-bottom-right"]);
        }
    }

    public function details($id){

        $question = DB::table('academy_questions')
                        ->select('academy_questions.id','academy_questions.question_type_id','academy_questions.question_text','academy_questions.academy_type',
                        'academy_questions.question_type_id','subjects.title as subject_name','chapters.title as chapter_name','topics.title as topic_name','academy_types.title as academy_type_name',
                        'question_types.title as question_type','academy_questions.created_at','academy_questions.difficulty_level','academy_questions.marks',
                        'mcq_questions.option_1','mcq_questions.option_2','mcq_questions.option_3','mcq_questions.option_4','mcq_questions.option_5','mcq_questions.correct_option',
                        'academy_questions.hint','academy_questions.question_detail','academy_questions.solution')
                        ->where('academy_questions.id',$id)
                        ->where('academy_questions.created_by',auth()->id())
                        ->leftJoin('subjects','academy_questions.subject_id','subjects.id')
                        ->leftJoin('chapters','academy_questions.chapter_id','chapters.id')
                        ->leftJoin('topics','academy_questions.topic_id','topics.id')
                        ->leftJoin('academy_types','academy_questions.academy_type','academy_types.id')
                        ->leftJoin('question_types','academy_questions.question_type_id','question_types.id')
                        ->leftJoin('mcq_questions','academy_questions.id','mcq_questions.question_id')
                        // ->leftJoin('question_histories','academy_questions.id','question_histories.question_id')
                        // ->leftJoin('institutes','question_histories.institute_id','institutes.id')
                        // ->leftJoin('units','question_histories.unit_id','units.id')
                        // ->leftJoin('questions_board_history','academy_questions.id','questions_board_history.question_id')
                        ->first();
                        
        return view("admin.question.details",compact('question'));

    }

    public function chapterBaseQuestion($id){

        $questions = DB::table('academy_questions')
                        ->select('academy_questions.id','academy_questions.question_type_id','academy_questions.question_text','academy_questions.academy_type',
                        'academy_questions.question_type_id','subjects.title as subject_name','chapters.title as chapter_name','academy_types.title as academy_type_name',
                        'question_types.title as question_type','academy_questions.created_at')
                        ->where('academy_questions.chapter_id',$id)
                        ->where('academy_questions.created_by',auth()->id())
                        ->leftJoin('subjects','academy_questions.subject_id','subjects.id')
                        ->leftJoin('chapters','academy_questions.chapter_id','chapters.id')
                        ->leftJoin('academy_types','academy_questions.academy_type','academy_types.id')
                        ->leftJoin('question_types','academy_questions.question_type_id','question_types.id')
                        ->latest()
                        ->paginate(10);

        $chapter = DB::table('chapters')->where('id',$id)->first('title');

        return view("admin.question.chapter_base",compact('questions','chapter'));

    }

    public function subjectBaseQuestion($id){

        $questions = DB::table('academy_questions')
                        ->select('academy_questions.id','academy_questions.question_type_id','academy_questions.question_text','academy_questions.academy_type',
                        'academy_questions.question_type_id','subjects.title as subject_name','chapters.title as chapter_name','academy_types.title as academy_type_name',
                        'question_types.title as question_type','academy_questions.created_at')
                        ->where('academy_questions.subject_id',$id)
                        ->where('academy_questions.created_by',auth()->id())
                        ->leftJoin('subjects','academy_questions.subject_id','subjects.id')
                        ->leftJoin('chapters','academy_questions.chapter_id','chapters.id')
                        ->leftJoin('academy_types','academy_questions.academy_type','academy_types.id')
                        ->leftJoin('question_types','academy_questions.question_type_id','question_types.id')
                        ->latest()
                        ->paginate(10);

        $subject = DB::table('subjects')->where('id',$id)->first('title');

        return view("admin.question.subject_base",compact('questions','subject'));

    }

    public function topicBaseQuestion($id){

        $questions = DB::table('academy_questions')
                        ->select('academy_questions.id','academy_questions.question_type_id','academy_questions.question_text','academy_questions.academy_type',
                        'academy_questions.question_type_id','subjects.title as subject_name','chapters.title as chapter_name','academy_types.title as academy_type_name',
                        'question_types.title as question_type','academy_questions.created_at')
                        ->where('academy_questions.topic_id',$id)
                        ->where('academy_questions.created_by',auth()->id())
                        ->leftJoin('subjects','academy_questions.subject_id','subjects.id')
                        ->leftJoin('chapters','academy_questions.chapter_id','chapters.id')
                        ->leftJoin('academy_types','academy_questions.academy_type','academy_types.id')
                        ->leftJoin('question_types','academy_questions.question_type_id','question_types.id')
                        ->latest()
                        ->paginate(10);

        $topic = DB::table('topics')->where('id',$id)->first('title');

        return view("admin.question.topic_base",compact('questions','topic'));

    }

    public function delete($id){

        dd('delete',$id);// neew to be complete delete logic
    }

    public function cacheClearLatestQuestionInformation(){
        Cache::forget('latest_question_info_' . auth()->id());
    }
    
    private function updateMcqQuestion($request, $chapter_id, $id)
    {
        // Reformat options
        $mcqQuestion = [
            'option_1' => $this->reformateDom($request->option_1 ?? null, $chapter_id),
            'option_2' => $this->reformateDom($request->option_2 ?? null, $chapter_id),
            'option_3' => $this->reformateDom($request->option_3 ?? null, $chapter_id),
            'option_4' => $this->reformateDom($request->option_4 ?? null, $chapter_id),
            'correct_option' => $request->correct_option,
        ];
        if ($request->question_type_id == 4) {
            $mcqQuestion['option_5'] = $this->reformateDom($request->option_5 ?? null, $chapter_id);
        }
        // Check if question exists
        $existingQuestion = McqQuestion::where('question_id', $id);
        // Update or create the question
        if ($existingQuestion->exists()) {
            $existingQuestion->update($mcqQuestion);
        } else {
            McqQuestion::create(array_merge(['question_id' => $id], $mcqQuestion));
        }
    }


    private function updateQuestionHistorys($request,$id){

        $question_histories_id = $request->question_histories_id ?? [];
        $institute_id = $request->institute_id ?? [];
        $unit_id = $request->unit_id ?? [];
        $years = $request->years ?? [];
        
        $questionHistorys = array_map(function ($question_histories_id,$institute_id, $unit_id, $years) {
            return [
                "id"=>$question_histories_id,
                "institute_id" => $institute_id,
                "unit_id" => $unit_id,
                "years" => $years,
            ];
        }, $question_histories_id,$institute_id, $unit_id, $years);

        foreach ($questionHistorys as $questionHistory) {
            $questionHistory["question_id"] = $id;
            QuestionHistory::where('question_id', $id)
            ->updateOrCreate(
                ["id"=>$questionHistory['id']],
                $questionHistory
            );
        }
        // delete not in history
        QuestionHistory::where('question_id', $id)->whereNotIn('id', (array_column($questionHistorys, 'id')?? []))->delete();
    }

    private function updateQuestionsBoardHistorys($request,$id){

        $board_histories_id = $request->board_histories_id;
        $board_name = $request->board_name;
        $board_year = $request->board_year;
        $questionsBoardHistorys = array_map(function ($board_histories_id,$board_name, $board_year) {
            return [
                "id"=> $board_histories_id,
                "board_name" => $board_name,
                "year"       => $board_year,
            ];
        },$board_histories_id,$board_name, $board_year);

        foreach ($questionsBoardHistorys as $questionsBoardHistory) {
            $questionsBoardHistory["question_id"] = $id;
            QuestionsBoardHistory::where('question_id', $id)
            ->updateOrCreate(
                ["id" => $questionsBoardHistory['id']],
                $questionsBoardHistory
            );
        }
        // delete not in history
        QuestionsBoardHistory::where('question_id', $id)->whereNotIn('id', (array_column($questionsBoardHistorys, 'id')?? []))->delete();
    }

    public function genarateFackQuestion(){
        $faker = FakerFactory::create();
       for($i=0;$i<176;$i++){
            $this->createFackQuestion($faker);
        }
        return 'success';
    }

    private function createFackQuestion($faker){
        $board_name = ['dhaka','rajshahi','comilla'];
        $board_year = ['2019','2020','2021'];
        $institute_id = ['1','3'];
        $unit_id = ['5','6'];
        $years = ['2018','2019'];
        $chapter_id = $faker->numberBetween(6, 12);
        if(count($institute_id) != count($years)){
            return redirect()->back()->with('error', 'Invalid Question History!');
        }
        if(count($board_name) != count($board_year)){
            return redirect()->back()->with('error', 'Invalid Question History!');
        }
        $history_count=count($institute_id);
        
        $question_id = AcademyQuestion::create([
            'chapter_id'=>$chapter_id,
            'subject_id' => 4,
            'question_type_id'=>1,
            'difficulty_level'=>$faker->randomElement(['10', '20', '30','40','50','60','70','80','90','100']),
            'reference_id'=>$faker->numberBetween(1, 10),
            'marks'=> $faker->numberBetween(1, 10),
            'hint'=> $faker->sentence,
            'question_text'=>$this->reformateDom($faker->sentence,$chapter_id),
            'question_detail'=>$this->reformateDom($faker->sentence,$chapter_id),
            'solution'=>$this->reformateDom($faker->sentence,$chapter_id),
            'academy_type' =>$faker->numberBetween(1, 2),
            'created_by' => auth()->user()->id,
        ])->id;

        $data = [
            'question_id' => $question_id,
            'option_1' => $this->reformateDom('A', $chapter_id),
            'option_2' => $this->reformateDom('B', $chapter_id),
            'option_3' => $this->reformateDom('C', $chapter_id),
            'option_4' => $this->reformateDom('D', $chapter_id),
            'correct_option' => 'option_3',
        ];
        // Create the McqQuestion
        McqQuestion::create($data);            
    
        for($i=0;$i<$history_count;$i++){
            if($institute_id[$i]!=null && $years[$i]!=null){
                QuestionHistory::create([
                    'question_id'=>$question_id,
                    'institute_id'=>$institute_id[$i],
                    'unit_id'=>$unit_id[$i],
                    'years'=>$years[$i],
                ]);
            }
        }
        for( $y=0; $y<count($board_name); $y++){
            if($board_name[$y]!= null && $board_year[$y]!= null){
                QuestionsBoardHistory::create([
                    'question_id'=>$question_id,
                    'board_name'=>$board_name[$y],
                    'year'=>$board_year[$y],
                ]);
            }
        }
    }

    private function cacheLatestQuestionInfo($requestedInfo){
        $userId = auth()->id();
        Cache::remember('latest_question_info_' . $userId, 60 * 60 * 24, function () use ($requestedInfo) {
            return [
                'academy_type'=>$requestedInfo['academy_type'],
                'subject_id'=>$requestedInfo['subject_id'],
                'chapter_id'=>$requestedInfo['chapter_id'],
                'topic_id'=>$requestedInfo['topic_id'],
                'question_type_id'=>$requestedInfo['question_type_id'],
                'difficulty_level'=>$requestedInfo['difficulty_level'],
                'marks'=>$requestedInfo['marks'],
            ];
        });
    }
}
