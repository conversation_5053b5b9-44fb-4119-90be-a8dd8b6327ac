<?php

namespace App\Http\Controllers\Instructor;

use App\Models\AcademyType;
use Illuminate\Http\Request;
use App\Models\RoutineUpload;
use App\Traits\ToastrMessage;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Storage;

class RoutineUploadController extends Controller
{
    use ToastrMessage;

    public function index(Request $request)
    {
        $query = RoutineUpload::with('academyType:id,title')->where('instructor_id', auth()->user()->id);
        if ($request->search_name != null) {
            $routines = $query
            ->where('title', 'like', '%' . $request->search_name . '%')
            ->orWhereHas('academyType', function ($query) use ($request) {
                $query->where('title', 'like', '%' . $request->search_name . '%');
            })
            ->paginate(10);
        } else {
            $routines = $query->paginate(10);
        }
        return view('instructor.routine_upload.index', compact('routines'));
    }

    public function create()
    {
        $academyTypes = AcademyType::where('status', 1)->get();

        return view('instructor.routine_upload.add', compact('academyTypes'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required',
            'academic_type' => 'required',
            'file' => 'required|mimes:pdf|max:2048',
        ]);
        $file = $request->file('file');
        $file_name = time() . '.' . $file->getClientOriginalExtension();

        Storage::disk('public')->put('routine/' . $file_name, file_get_contents($file));
        RoutineUpload::create([
            'title' => $request->title,
            'academic_type' => $request->academic_type,
            'instructor_id' => auth()->user()->id,
            'file_path' => 'routine/' . $file_name,
        ]);
        $this->success('Routine uploaded successfully');
        return redirect()->route('instructor.routine-index');
    }

    public function destroy(RoutineUpload $routine)
    {
        Storage::disk('public')->delete($routine->file_path);
        $routine->delete();
        $this->success('Routine deleted successfully');
        return back();
    }

    public function download($id)
    {
        // Find the routine by ID
        $routine = RoutineUpload::find($id);
        // Check if the routine exists
        if (!$routine) {
            $this->error('Routine not found');
            return back();
        }
        // Define the custom download file name
        $fileName = $routine->title . '.' . pathinfo($routine->file_path, PATHINFO_EXTENSION);
    
        // Return the file for download with the custom name
        return Storage::disk('public')->download($routine->file_path, $fileName);
    }
    

    public function showAllRoutines(Request $request)
    {
        $query = RoutineUpload::with('academyType:id,title')->where('status', 1);
        if($request->instructor_id !=null){
            $routines = $query->where('instructor_id',$request->instructor_id);
        }
        if ($request->search_name != null) {
           
            $routines = $query
            ->where('title', 'like', '%' . $request->search_name . '%')
            ->orWhereHas('academyType', function ($query) use ($request) {
                $query->where('title', 'like', '%' . $request->search_name . '%');
            })
            ->paginate(10);
        } else {
            $routines = $query->paginate(10);
        }
        return view('instructor.all-teacher-routine', compact('routines'));
    }

    public function statusChange(Request $request)
    {  
        $routine = RoutineUpload::find($request->id);
        if (!$routine) {
            $this->error('Routine not found');
            return response()->json(['status'=>false,'message' => 'error']);
        }
        $routine->update([
            'status' => $routine->status == 1 ? 0 : 1,
        ]);
        $message = $routine->status == 1 ? 'Active' : 'Deactivated';
        return response()->json(['status'=>true,'message' => 'Routine '.$message.' successfully']);
    }


}
