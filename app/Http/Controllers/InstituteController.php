<?php

namespace App\Http\Controllers;
use App\Models\Institute;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;

class InstituteController extends Controller
{
    public function index()
    {
        $institutes = Institute::latest()->paginate(10);
        return view('admin.institute.view', compact('institutes'));
    }

    public function instituteStore(Request $request)
    {
        $data = $request->validate([
            'title' => 'required',
        ]);
        Institute::create($data);
        return redirect()->route('admin.institute.index')->with('success', 'Institute created successfully.');
    }

    public function edit($id)
    {   
        $institute = Institute::where('id',$id)->first();
        return response()->json($institute);
    }

    public function update(Request $request, Institute $institute)
    {
        $request->validate([
            'title' => 'required',
        ]);
        DB::table('institutes')->where('id',$request->institute_id)->update(['title' => $request->title]);
        $this->showToastrMessageForSubject('success', __('Institute updated successfully.'));
        return redirect()->route('admin.institute.index');
    }

    public function showToastrMessageForSubject($type, $message)
    {
        switch ($type) {
            case 'success':
                return toastr()->success($message, '', ["positionClass" => "toast-bottom-right"]);
                break;
            case 'warning':
                return toastr()->warning($message, '', ["positionClass" => "toast-bottom-right"]);
                break;
            case 'error':
                return toastr()->error($message, '', ["positionClass" => "toast-bottom-right"]);
                break;
            default:
                return toastr()->success($message, '', ["positionClass" => "toast-bottom-right"]);
        }
    }


}
