<?php

namespace App\Http\Controllers;
use App\Models\Test;
use App\Models\Topic;
use App\Models\Chapter;
use App\Models\Subject;
use App\Models\ExamBatch;
use Illuminate\Http\Request;
use App\Models\ExamManagement;
use Illuminate\Support\Facades\DB;
use App\Models\ResultGradingProcess;
use App\Models\ExamManagementAcademyType;
use App\Models\ExamManagementConfigurations;
use App\Models\ExamManagementConfigurationSubjects;
use App\Models\ExamManagementConfigurationChapterTopics;
use App\Models\ExamManagementConfigurationSubjectChapters;

class ExamManagementController extends Controller
{
    // All Exam Lists Show 
    public function all(Request $request){
        $data =[];
        $filter = $request->filter ?? '';
        $query = ExamManagement::where('created_by',auth()->id());

        if ($filter === 'live') {
            $query->whereDate('exam_management.starts_at', '=', now()->toDateString())
                  ->where(function ($q) {
                      $q->where('exam_management.exam_time_end', '>=', now())
                        ->orWhereNull('exam_management.exam_time_end');
                  });
        } elseif ($filter === 'previous') {
            $query->where(function ($q) {
                $q->whereDate('exam_management.exam_time_end', '<', now())
                  ->orWhere('exam_management.starts_at', '<', now());
            });
        } elseif ($filter === 'upcoming') {
            $query->whereDate('exam_management.starts_at', '>', now());
        }
        if($request->search_name != null){
            $data['exams'] = $query->where('title', 'like', '%' . $request->search_name . '%')
            ->orderBy('id', 'desc')
            ->paginate(10);
        }else $data['exams'] = $query->orderBy('id', 'desc')->paginate(10);

        return view('admin.exam_manage.view', $data);    

    }

    // Exam Add Page Return 
    public function add(){

        $acadymic_types = DB::table('academy_types')->where('status',1)->get();
        $exam_types = DB::table('exam_types')->get();
        $question_types = DB::table('question_types')->get();
        $batches = DB::table('batches')->where('status',1)->where('instructor_id',auth()->id())->get();

        return view('admin.exam_manage.create',compact(
            'acadymic_types',
            'exam_types',
            'question_types',
            'batches'
        ));  

    }

    // Exam Slug Create 
    public function create_slug(){
        // Define the length of the random slug (between 20 and 30 characters)
        $length = rand(20, 30);
        // Define a pool of characters for the slug (lowercase letters and numbers)
        $characters = 'abcdefghijklmnopqrstuvwxyz0123456789';
        // Shuffle the characters and take a random subset
        return substr(str_shuffle(str_repeat($characters, $length)), 0, $length);
    }

    // Exam Create 
    public function create(Request $request){

        $user = auth()->user() ;
        $slug = $this->create_slug();
        $exam = ExamManagement::create([

            'title' => $request->title,
            'institute_id' => $request->institute_id,
            'unit_id' => $request->unit_id ?? null,
            'type_id' => $request->type_id,
            'fee' => $request->fee,
            'total_duration' => $request->total_duration,
            'number_of_question' => $request->number_of_question,
            'marks_per_question' => $request->marks_per_question,
            'negative_marks_per_question' => $request->negative_marks_per_question,
            'model' => $request->model,
            'result_type' => $request->result_type,
            'exam_type_id' => $request->exam_type_id,
            'extra_time' =>  $request->extra_time,
            'slug' => $slug,
            'status' => $request->status,
            'created_by' => $user->id,
            'starts_at' => $request->starts_at,
            'is_expire_date' => $request->is_expire_date ?? null,
            
        ]);

        $academyTypeIdsData = [] ;
        if (count($request->academy_type_ids) > 0) {
            foreach ($request->academy_type_ids as $academy_type_id) {
                $academyTypeIdsData[] = [
                    'academy_type_id' => $academy_type_id,
                    'exam_management_id' => $exam->id, // Assuming this value comes from the request
                ];
            }
        }

        ExamManagementAcademyType::insert($academyTypeIdsData);

        // Data Insert To Batches Start
        $batches = [] ;
        if (count($request->batches) > 0) {
            foreach ($request->batches as $batch) {
                $batches[] = [
                    'batch_id' => $batch,
                    'exam_management_id' => $exam->id, // Assuming this value comes from the request
                ];
            }
        }
        ExamBatch::insert($batches);
        // Data Insert To Batches End 
        
        ResultGradingProcess::insert([
            'exam_management_id' => $exam->id,
            'type' => $request->base,
            'cut_marks' => $request->base == '1' ? $request->marks_data : null,
            'pass_percentage' => $request->base == '2' ? $request->marks_data : null,
            'created_by' => $user->id,
        ]);

        $this->showToastrMessageForSubject('success', __('Exam successfully Save'));
        return redirect()->route('common.exam.examConfiguration', ['slug' => $exam->slug]);

    }

    // Exam Edit Page Return
    public function edit($id){

        $exam = ExamManagement::where('id',$id)->first();
        $acadymic_types = DB::table('academy_types')->where('status',1)->get();
        $exam_types = DB::table('exam_types')->get();
        $question_types = DB::table('question_types')->get();
        $exam_academy_types = ExamManagementAcademyType::where('exam_management_id',$id)->pluck('academy_type_id')->toArray();
        $result = ResultGradingProcess::where('exam_management_id',$id)->first();
        $batches = DB::table('batches')->where('status',1)->where('instructor_id',auth()->id())->get();
        
        return view('admin.exam_manage.edit',compact(
            'acadymic_types',
            'exam_types',
            'question_types',
            'exam',
            'exam_academy_types',
            'result',
            'batches'
        ));
    }

    // Exam Update
    public function update(Request $request,$id){

        $user = auth()->user() ;
        $exam = ExamManagement::where('id',$id)->first();
        $academy_type_ids = $request->academy_type_ids == null ? ExamManagementAcademyType::where('exam_management_id',$id)->pluck('academy_type_id')->toArray() : $request->academy_type_ids;
        $request->merge(['academy_type_ids' => $academy_type_ids]);
        $exam->update([

            'title' => $request->title,
            'institute_id' => $request->institute_id,
            'unit_id' => $request->unit_id ?? null,
            'type_id' => $request->type_id,
            'fee' => $request->fee,
            'total_duration' => $request->total_duration,
            'number_of_question' => $request->number_of_question,
            'marks_per_question' => $request->marks_per_question,
            'negative_marks_per_question' => $request->negative_marks_per_question,
            'model' => $request->model,
            'result_type' => $request->result_type,
            'exam_type_id' => $request->exam_type_id,
            'extra_time' =>  $request->extra_time,
            'status' => $request->status,
            'starts_at' => $request->starts_at,
            'is_expire_date' => $request->is_expire_date ?? null,
            
        ]);

        $academyTypeIdsData = [] ;
        if (count($request->academy_type_ids) > 0) {
            foreach ($request->academy_type_ids as $academy_type_id) {
                $academyTypeIdsData[] = [
                    'academy_type_id' => $academy_type_id,
                    'exam_management_id' => $exam->id, // Assuming this value comes from the request
                ];
            }
        }

        ExamManagementAcademyType::where('exam_management_id',$id)->delete();
        ExamManagementAcademyType::insert($academyTypeIdsData);


        // Data Insert To Batches Start
        $batches = [] ;
        if (count($request->batches) > 0) {
            foreach ($request->batches as $batch) {
                $batches[] = [
                    'batch_id' => $batch,
                    'exam_management_id' => $exam->id, // Assuming this value comes from the request
                ];
            }
        }
        // Get the existing batch IDs related to the current exam
        $existingBatchIds = $exam->batches->pluck('id')->toArray();

        // Get the batch IDs from the new selection
        $newBatchIds = array_column($batches, 'batch_id');

        // Find out which batch IDs need to be deleted (those that are not selected anymore)
        $batchesToDelete = array_diff($existingBatchIds, $newBatchIds);
        
        // Find out which batch IDs need to be added (those that are newly selected)
        $batchesToAdd = array_diff($newBatchIds, $existingBatchIds);

        // 1. **Delete the batches that are no longer selected**
        ExamBatch::where('exam_management_id', $exam->id)
            ->whereIn('batch_id', $batchesToDelete)
            ->delete();
        
        $newBatchData = [];

        foreach ($batchesToAdd as $batchId) {
            $newBatchData[] = [
                'exam_management_id' => $exam->id,
                'batch_id' => $batchId,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        // Bulk insert new batches if there are any to add
        if (!empty($newBatchData)) {
            ExamBatch::insert($newBatchData);
        }

        // Data Insert To Batches End

        ResultGradingProcess::where('exam_management_id',$id)->update([
            'type' => $request->base,
            'cut_marks' => $request->base == '1' ? $request->marks_data : null,
            'pass_percentage' => $request->base == '2' ? $request->marks_data : null,
            'created_by' => $user->id,
        ]);

        $this->showToastrMessageForSubject('success', __('Exam successfully Update'));
        // return redirect()->route('common.exam.examConfiguration', ['slug' => $exam->slug]);
        return redirect()->route('common.exam.index');

    }

    public function examConfiguration($slug){
        
        if($slug){
            $exam = ExamManagement::with('academyTypes')->where('slug',$slug)->first();
            if($exam){
                $academyTypeIds = ExamManagementAcademyType::where('exam_management_id', $exam->id)
                    ->pluck('academy_type_id')
                    ->toArray() ?? [];
                $subjects = Subject::where('created_by',auth()->id())->whereIn('academy_type',$academyTypeIds)->get();
                return view('admin.exam_manage.exam_configaration',compact('exam','subjects'));
            }
            
        }
       

    }

    public function examConfigurationStore(Request $request){

        // Validate request data
        $this->validate($request, [
            'exam_management_id' => 'required',
            'title' => 'required',
            'subject_id' => 'required|array',
            'min_difficulty' => 'required|array',
            'max_difficulty' => 'required|array',
            'chapters' => 'required|array',
            'topics' => 'array',
        ]);

        $exam = DB::table('exam_management')->where('id',$request->exam_management_id)->select('id','slug')->first();

        $exManConf = DB::table('exam_management_configurations')->where('exam_management_id',$exam->id)->exists();

        if($exam && $exManConf != true){
            // Create the exam configuration
            $config_id = ExamManagementConfigurations::create([
                'title' => $request->title ?? null,
                'exam_management_id' => $request->exam_management_id,
                'status' => 1,
            ])->id;

            // Loop through subjects, difficulties, and chapters
            foreach ($request->subject_id as $index => $subject_id) {
                // Create subject configuration for each subject
                $configSubjectId = ExamManagementConfigurationSubjects::create([
                    'exam_management_configurations_id' => $config_id,
                    'subject_id' => $subject_id,
                    'min_difficulty' => $request->min_difficulty[$index],
                    'max_difficulty' => $request->max_difficulty[$index],
                    'created_by' => auth()->id(), // Assuming you want to log the user creating this
                ])->id;
                
                // Retrieve the chapters for this subject
                $chapters = $request->chapters[$subject_id] ?? [];

                // Loop through chapters and create chapter entries
                foreach ($chapters as $chapter_id) {
                    ExamManagementConfigurationSubjectChapters::create([
                        'c_subject_id' => $configSubjectId,
                        'chapter_id' => $chapter_id,
                    ]);

                    $topics = $request->topics[$chapter_id] ?? [];

                    foreach ($topics as $topic_id) {
                        ExamManagementConfigurationChapterTopics::create([
                            'chapter_id' => $chapter_id,
                            'topic_id' => $topic_id,
                        ]);
                    }
                }
            }

            $this->showToastrMessageForSubject('success', __('Exam Configaration Saved'));
            return redirect()->route('common.exam.index');

        }
        
    }

    public function editExamConfiguration($slug)
    {
        if (!$slug) {
            $this->showToastrMessageForSubject('error', __('Invalid exam slug'));
            return redirect()->back();
        }
        // Fetch the exam using Eloquent
        $exam = ExamManagement::where('slug', $slug)->first();
        
        if (!$exam) {
            $this->showToastrMessageForSubject('error', __('Exam not found'));
            return redirect()->back();
        }

        $academyTypeIds = ExamManagementAcademyType::where('exam_management_id', $exam->id)
            ->pluck('academy_type_id')
            ->toArray() ?? [];
        $subjects = Subject::where('created_by',auth()->id())->whereIn('academy_type',$academyTypeIds)->get();

        return view('admin.exam_manage.edit_exam_configaration',compact('exam','subjects'));
    }

    public function updateExamConfiguration(Request $request)
    {
        dd($request->all());
        // Validate request data
        // $this->validate($request, [
        //     'exam_management_id' => 'required',
        //     'title' => 'required',
        //     'subject_id' => 'required|array',
        //     'min_difficulty' => 'required|array',
        //     'max_difficulty' => 'required|array',
        //     'chapters' => 'required|array',
        //     'topics' => 'array',
        // ]);

        // $exam = DB::table('exam_management')->where('id',$request->exam_management_id)->select('id','slug')->first();

        // $exManConf = DB::table('exam_management_configurations')->where('exam_management_id',$exam->id)->exists();

        // if($exam && $exManConf == true){
        //     // Create the exam configuration
        //     $config_id = ExamManagementConfigurations::where('exam_management_id',$exam->id)->update([
        //         'title' => $request->title ?? null,
        //         'exam_management_id' => $request->exam_management_id,
        //         'status' => 1,
        //     ]);

        //     // Loop through subjects, difficulties, and chapters
        //     foreach ($request->subject_id as $index => $subject_id) {
        //         // Create subject configuration for each subject
        //         $configSubjectId = ExamManagementConfigurationSubjects::where('exam_management_configurations_id',$config_id)->update([
        //             'subject_id' => $subject_id,
        //             'min_difficulty' => $request->min_difficulty[$index],
        //             'max_difficulty' => $request->max_difficulty[$index],
        //             'created_by' => auth()->id(), // Assuming you want to log the user creating this
        //         ]);
                
        //         // Retrieve the chapters for this subject
        //         $chapters = $request->chapters[$subject_id] ?? [];

        //         // Loop through chapters and create chapter entries
        //         foreach ($chapters as $chapter_id) {
        //             ExamManagementConfigurationSubjectChapters::where('c_subject_id',$configSubjectId)->update([
        //                 'chapter_id' => $chapter_id,
        //             ]);

        //             $topics = $request->topics[$chapter_id] ?? [];

        //             foreach ($topics as $topic_id) {
        //                 ExamManagementConfigurationChapterTopics::where('chapter_id',$chapter_id)->update([
        //                     'topic_id' => $topic_id,
        //                 ]);
        //             }
        //         }
        //     }

        //     $this->showToastrMessageForSubject('success','Exam Configaration Updated');
        //     return redirect()->route('common.exam.index');
        
        // }
    }

    public function examManageQuestions($slug)
    {
        // dd(request()->all());    
        if (!$slug) {
            $this->showToastrMessageForSubject('error', __('Invalid exam slug'));
            return redirect()->back();
        }

        // Fetch the exam using Eloquent
        $exam = ExamManagement::where('slug', $slug)->first();
        
        if (!$exam) {
            $this->showToastrMessageForSubject('error', __('Exam not found'));
            return redirect()->back();
        }

        // Fetch the exam configurations with necessary joins
        $examManagementConfigurationData = DB::table('exam_management_configurations')
        ->where('exam_management_configurations.exam_management_id', $exam->id)
        ->join('exam_management_configuration_subjects', 'exam_management_configuration_subjects.exam_management_configurations_id', '=', 'exam_management_configurations.id')
        ->leftJoin('exam_management_configuration_subject_chapters', 'exam_management_configuration_subjects.id', '=', 'exam_management_configuration_subject_chapters.c_subject_id')
        ->leftJoin('exam_management_configuration_chapter_topics', 'exam_management_configuration_subject_chapters.chapter_id', '=', 'exam_management_configuration_chapter_topics.chapter_id')
        ->select(
            'exam_management_configuration_subjects.id as subject_id',
            'exam_management_configuration_subject_chapters.chapter_id',
            'exam_management_configuration_chapter_topics.topic_id'
        )
        ->get();
    
        // Extract the necessary collections
        $allSubjectIds = $examManagementConfigurationData->pluck('subject_id')->filter()->unique()->toArray();
        $allChapterIds = $examManagementConfigurationData->pluck('chapter_id')->filter()->unique()->toArray();
        $allTopicIds = $examManagementConfigurationData->pluck('topic_id')->filter()->unique()->toArray();
        
        // Fetch academy type IDs
        $examManagementAcademyTypes = DB::table('exam_management_academy_type')
            ->where('exam_management_id', $exam->id)
            ->pluck('academy_type_id')
            ->toArray();

        // Build the base query for questions
        $allQuestionsQuery = DB::table('academy_questions')
        ->when(!empty($allTopicIds), function ($query) use ($allTopicIds) {
            return $query->whereIn('academy_questions.topic_id', $allTopicIds);
        })
        ->when(empty($allTopicIds) && !empty($allChapterIds), function ($query) use ($allChapterIds) {
            return $query->whereIn('academy_questions.chapter_id', $allChapterIds);
        })
        ->when(empty($allTopicIds) && empty($allChapterIds) && !empty($allSubjectIds), function ($query) use ($allSubjectIds) {
            return $query->whereIn('academy_questions.subject_id', $allSubjectIds);
        })
        ->whereIn('academy_questions.academy_type', $examManagementAcademyTypes);
    
        if ($exam->type_id == 1) {
            $allQuestions = $allQuestionsQuery
                ->join('mcq_questions', 'mcq_questions.question_id', '=', 'academy_questions.id')
                ->join('subjects', 'academy_questions.subject_id', '=', 'subjects.id')
                ->join('chapters', 'academy_questions.chapter_id', '=', 'chapters.id')
                ->leftJoin('topics', 'academy_questions.topic_id', '=', 'topics.id')
                ->select(
                    'academy_questions.id',
                    'academy_questions.question_text',
                    'academy_questions.hint',
                    'academy_questions.solution',
                    'academy_questions.question_detail',
                    'academy_questions.question_type_id',
                    'mcq_questions.option_1',
                    'mcq_questions.option_2',
                    'mcq_questions.option_3',
                    'mcq_questions.option_4',
                    'mcq_questions.option_5',
                    'mcq_questions.correct_option',
                    'academy_questions.subject_id',
                    'subjects.title as subject_name',
                    'academy_questions.chapter_id',
                    'chapters.title as chapter_name',
                    'academy_questions.topic_id as topic_id',
                    'topics.title as topic_name'
                )
                ->get();
        } else {
            $allQuestions = $allQuestionsQuery
                ->select(
                    'academy_questions.id',
                    'academy_questions.question_text',
                    'academy_questions.hint',
                    'academy_questions.solution',
                    'academy_questions.question_detail',
                    'academy_questions.subject_id',
                    'academy_questions.chapter_id',
                    'academy_questions.topic_id'
                )
                ->get();
        }

        $questionConfigurations = DB::table('academy_group_exam_questions')
            ->where('group_id', $exam->id)
            ->pluck('question_id')
            ->toArray();

        return view('admin.exam_manage.question_configaration', [
            'exam' => $exam,
            'allSubjectIds' => $allSubjectIds,
            'allChapterIds' => $allChapterIds,
            'allTopicIds' => $allTopicIds,
            'allQuestions' => $allQuestions,
            'questionConfigurations' => $questionConfigurations
        ]);
    }

    public function examManageQuestionsStore(Request $request)
    {
        $this->validate($request, [
            'selected_questions' => 'required|array',
        ]);
        $selectedQuestions = array_map(function ($question) {
            $decodedQuestion = json_decode($question, true);
            $decodedQuestion['created_at'] = now();
            $decodedQuestion['updated_at'] = now();
            return $decodedQuestion;
        }, $request->selected_questions ?? []);

        $exam = ExamManagement::where('id',($selectedQuestions[0]['group_id'] ?? 0))->pluck('number_of_question')->first();
        if(count($selectedQuestions) > $exam){
            $this->showToastrMessageForSubject('success', __('Your max question add limit is'. $exam)); 
            return back();
        }

        DB::table('academy_group_exam_questions')->upsert(
            $selectedQuestions, 
            ['group_id', 'question_id'] 
        );
        // Delete the questions that are not in the selected questions
        DB::table('academy_group_exam_questions')
            ->where('group_id', $selectedQuestions[0]['group_id'])
            ->whereNotIn('question_id', array_column($selectedQuestions, 'question_id') ?? [])
            ->delete();
        $this->showToastrMessageForSubject('success', __('Questions added to the exam'));
        return redirect()->route('common.exam.index');
    }

    public function getChapters($subject_id)
    {
        // Fetch chapters based on the subject ID
        $chapters = Chapter::where('subject_id', $subject_id)->where('status',1)->get();

        // Return the chapters as a JSON response
        return response()->json([
            'chapters' => $chapters
        ]);
    }

    public function getTopics(Request $request)
    {
        $chapterIds = $request->input('chapter_ids', []);
        $topics = Topic::whereIn('chapter_id', $chapterIds)->get()->groupBy('chapter_id');

        $html = view('admin.exam_manage.get_topics_view', compact('topics'))->render();
        return response()->json(['html' => $html]);
    }

    public function examApprovedAndUnapproved()
    {
        $exam =  ExamManagement::where('id', request()->id)->first();
        if (!$exam) {
            return response()->json(['status'=>false,'message' => 'Exam not found']);
        }
        $questionAddCount = DB::table('academy_group_exam_questions')->where('group_id', $exam->id)->count();
        $message = request()->is_approved == '1' ? 'unapproved' : 'approved';
        if(request()->is_approved == '0' && $exam->number_of_question == $questionAddCount){
            $exam->update(['is_approved' => request()->is_approved == '1' ? 0 : 1]);
            return response()->json(['status'=>true,'message' => 'Exam '.$message.' successfully']);
        }else if(request()->is_approved == '1'){
            $exam->update(['is_approved' => 0]);
            return response()->json(['status'=>true,'message' => 'Exam '.$message.' successfully']);
        }else{
            return response()->json(['status'=>false,'message' => 'Please add '.$exam->number_of_question.' questions to approve the exam']);
        }
    }

    public function showToastrMessageForSubject($type, $message)
    {
        switch ($type) {
            case 'success':
                return toastr()->success($message, '', ["positionClass" => "toast-bottom-right"]);
                break;
            case 'warning':
                return toastr()->warning($message, '', ["positionClass" => "toast-bottom-right"]);
                break;
            case 'error':
                return toastr()->error($message, '', ["positionClass" => "toast-bottom-right"]);
                break;
            default:
                return toastr()->success($message, '', ["positionClass" => "toast-bottom-right"]);
        }
    }

    public function examReport($id){
        $data = [];
        $data['reports']= Test::where('exam_id',$id)
                        ->with('user')
                        ->orderBy('earned_marks','desc')
                        ->orderBy('time_taken_in_second','asc')
                        ->paginate(10);

        return view('admin.exam_manage.report', $data);   

    }

    public function delete($id)
    {
        // Find the ExamManagement record by ID
        $exam = ExamManagement::find($id);
        if (!$exam) {
            $this->showToastrMessageForSubject('error', __('Exam not found'));
            return redirect()->back();
        }
        //Step 1: Delete related records in ExamManagementAcademyType
        ExamManagementAcademyType::where('exam_management_id', $exam->id)->delete();
        // Step 2: Delete related records in ResultGradingProcess
        ResultGradingProcess::where('exam_management_id', $exam->id)->delete();
        // Step 3: Handle ExamManagementConfigurations and its related data
        $config = ExamManagementConfigurations::where('exam_management_id', $id)->first();

        if ($config) {
            // Fetch all subject configurations related to the configuration
            $subjectConfigs = ExamManagementConfigurationSubjects::where('exam_management_configurations_id', $config->id)->get();
      
            foreach ($subjectConfigs as $subjectConfig) {
                // Step 4: Get chapters associated with the subject configuration
                $chapters = ExamManagementConfigurationSubjectChapters::where('c_subject_id', $subjectConfig->id)->pluck('chapter_id');
                // Step 5: Delete related topics for each chapter
                foreach ($chapters as $chapter_id) {
                    ExamManagementConfigurationChapterTopics::where('chapter_id', $chapter_id)->delete();
                }
                // Step 6: Delete related chapters for each subject configuration
                ExamManagementConfigurationSubjectChapters::where('c_subject_id', $subjectConfig->id)->delete();
                // Step 7: Delete the subject configuration itself
                $subjectConfig->delete();
            }
            // Step 8: Delete the configuration entry
            $config->delete();
        }
        // Step 9: Finally, delete the ExamManagement record
        $exam->delete();
        $this->showToastrMessageForSubject('success', __('Exam deleted successfully'));
        return redirect()->route('common.exam.index');
    }

    public function genarateExamLink(){
        $examId  = request()->exam_id;
        $model  = request()->model;
        $encryptExamId = customEncrypt($examId);
        $url = route('student.exam.redirectExamCenter', ['model' => $model, 'id' => $encryptExamId]);
        return response()->json([
            'status' => true,
            'url' => $url
        ]);
    }
}
