<?php

namespace App\Http\Controllers;
// use App\Http\Controllers\Subject;
use App\Models\Subject;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use App\Traits\General;

class SubjectController extends Controller
{
    public function index(Request $request){

        $query = Subject::where('created_by',auth()->id())->with('user','academyType');

        if($request->search_name != null){
            $subjects = $query
            ->where('title', 'like', '%' . $request->search_name . '%')
            ->orWhereHas('academyType', function ($query) use ($request) {
                $query->where('title', 'like', '%' . $request->search_name . '%');
            })
            ->paginate(15);

        }else $subjects = $query->paginate(15);
        return view('admin.subject.view', compact('subjects'));   
    }

    public function add(){

        $acadymic_types = DB::table('academy_types')->where('status',1)->get();
        return view('admin.subject.create',compact('acadymic_types'));  

    }

    public function subjectStore(Request $request){

        $validator = Validator::make($request->all(), [
            'title' => 'required',
            'academy_type' => 'required',
            'status' => 'required'
        ]);
        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }
        $subject = Subject::create([
            'title' => $request->title,
            'academy_type' => $request->academy_type,
            'status' => $request->status,
            'created_by' => auth()->id()
        ]);
        $this->showToastrMessageForSubject('success', __('Subject successfully Created'));
        return redirect()->back();
    }

    public function edit($id){
        $subject = DB::table('subjects')->where('created_by',auth()->id())->where('id',$id)->first();
        $acadymic_types = DB::table('academy_types')->where('status',1)->get();
        return view('admin.subject.edit',compact('acadymic_types','subject')); 
    }

    public function update($id, Request $request){

        $validator = Validator::make($request->all(), [
            'title' => 'required',
            'academy_type' => 'required',
            'status' => 'required'
        ]);
        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        Subject::where('id',$id)->update([
            'title' => $request->title,
            'academy_type' => $request->academy_type,
            'status' => $request->status,
            'created_by' => auth()->id()
        ]);
        $this->showToastrMessageForSubject('success', __('Subject successfully Updated'));
        return redirect()->route('instructor.subject.index');

    }

    public function delete($id){

        if(DB::table('chapters')->where('created_by',auth()->id())->where('subject_id',$id)->exists()){
            $this->showToastrMessageForSubject('success', __('Subject cannot be deleted because it has questions associated with it'));
        }else{
            DB::table('subjects')->where('id',$id)->where('created_by',auth()->id())->delete();
            $this->showToastrMessageForSubject('success', __('Subject successfully deleted'));
        }
        return redirect()->route('instructor.subject.index');

    }

    public function showToastrMessageForSubject($type, $message)
    {
        switch ($type) {
            case 'success':
                return toastr()->success($message, '', ["positionClass" => "toast-bottom-right"]);
                break;
            case 'warning':
                return toastr()->warning($message, '', ["positionClass" => "toast-bottom-right"]);
                break;
            case 'error':
                return toastr()->error($message, '', ["positionClass" => "toast-bottom-right"]);
                break;
            default:
                return toastr()->success($message, '', ["positionClass" => "toast-bottom-right"]);
        }
    }
}
