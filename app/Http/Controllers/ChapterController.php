<?php

namespace App\Http\Controllers;

use App\Models\Chapter;
use App\Traits\General;
use Illuminate\Http\Request;
use App\Traits\ToastrMessage;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class ChapterController extends Controller
{
    use ToastrMessage;
    
    public function index(Request $request){
        $data =[];

        $query = DB::table('chapters')->where('chapters.created_by',auth()->id())->select('chapters.*','subjects.title as subject_title')
                    ->leftJoin('subjects','chapters.subject_id','subjects.id')->latest();
        
        if($request->search_name != null){
            $data['chapters'] = $query->where('chapters.title', 'like', '%' . $request->search_name . '%')->paginate(10);
        }else $data['chapters'] = $query->paginate(10);

        return view('admin.chapter.view', $data); 
    }

    public function add(){

        $subjects = DB::table('subjects')->where('subjects.created_by',auth()->id())->where('status',1)->latest()->get();
        return view('admin.chapter.create',compact('subjects'));

    }

    public function chapterStore(Request $request){

        $validator = Validator::make($request->all(), [
            'title' => 'required',
            'subject_id' => 'required',
            'status' => 'required'
        ]);
        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }
        $subject = Chapter::create([
            'title' => $request->title,
            'subject_id' => $request->subject_id,
            'status' => $request->status,
            'created_by' => auth()->id()
        ]);
        $this->success(__('Chapter successfully Created'));
        return redirect()->back();
    }

    public function edit($id){

        $chapter = DB::table('chapters')->where('chapters.created_by',auth()->id())->where('id',$id)->first();
        $subjects = DB::table('subjects')->where('subjects.created_by',auth()->id())->where('status',1)->latest()->get();

        return view('admin.chapter.edit',compact('chapter','subjects')); 

    }

    public function update($id, Request $request){

        $validator = Validator::make($request->all(), [
            'title' => 'required',
            'subject_id' => 'required',
            'status' => 'required'
        ]);
        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        Chapter::where('id',$id)->update([
            'title' => $request->title,
            'subject_id' => $request->subject_id,
            'status' => $request->status,
            'created_by' => auth()->id()
        ]);

        $this->success(__('Chapter successfully Updated'));
        return redirect()->route('instructor.chapter.index');

    }

    public function delete($id){

        if(DB::table('academy_questions')->where('academy_questions.created_by',auth()->id())->where('chapter_id',$id)->exists()){
            $this->success(__('Chapter cannot be deleted because it contains questions.'));
        }else{
            DB::table('chapters')->where('chapters.created_by',auth()->id())->where('id',$id)->delete();
            $this->success(__('Chapter successfully deleted'));
        }
        return redirect()->route('instructor.chapter.index');
    }
}
