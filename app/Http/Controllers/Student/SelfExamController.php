<?php

namespace App\Http\Controllers\Student;

use Carbon\Carbon;
use App\Models\SelfTest;
use Illuminate\Http\Request;
use App\Traits\ToastrMessage;
use App\Models\ExamManagement;
use App\Traits\ExamHelperTrait;
use App\Models\SelfQuestionAnswer;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Crypt;

class SelfExamController extends Controller
{
    use ToastrMessage ,ExamHelperTrait;
   
    public function getSelfExam(int $id){

        $key  = 'academy-self-exam-'.$id;
        $exam = $this->getExamById($id, $key);
        // if(isset($exam) && $this->checkAllValidationForExam($exam)){
        //     $isAlreadyEnrolled = $this->isAlreadyEnrolled($exam->id, auth()->id());
        //     $examStartsAt = Carbon::createFromFormat('d-m-Y h:i:s A', $exam->starts_at);
        //     if ( Carbon::now()->greaterThanOrEqualTo($examStartsAt) && $isAlreadyEnrolled) {
                return $this->processSelfExam($exam);
        //     }elseif(!$isAlreadyEnrolled){
        //         $this->examEnrolment($exam->id, auth()->id(),$exam->fee);
        //          return view('frontend.student.exam_center.count_down', compact('exam'));
        //     }else{
        //         return view('frontend.student.exam_center.count_down', compact('exam'));
        //     }
        // }else{
        //     cache()->forget($key);
        //     $this->info('This exam is not available');
        //     return back();
        // }
    }

    private function processSelfExam(object $exam){
        $testId = $this->isSelfTestRecordExist() ?: $this->createSelfTestRecord($exam->id, auth()->id());
        if($exam->type_id == 1){
            cache()->put('self-exam-runing-exam-test-id-'.auth()->user()->id, $testId, 86400);
            return redirect()->route('student.start-self-mcq-exam',['id' => Crypt::encryptString($testId)]);
        }else if($exam->type_id == 2){
            $this->info('This Short question exam module is under development');
        }else{
            $this->info('This CQ exam module is under development');
        }
    }

    private function createSelfTestRecord(int $examId, int $userId){
        $test_id = SelfTest::create([
            'user_id' => auth()->user()->id,
            'exam_id' =>$examId,
        ])->id;
        return $test_id;
    }

    public function startSelfMcqExam($id)
    {
        $enc_id=$id;
        $testId = Crypt::decryptString($id);
        $test= SelfTest::findOrFail($testId);
        $exam = cache()->get('academy-self-exam-'.$test->exam_id);
        $questions = Cache::remember('self-exam-rendom-questions-'.$testId,10800, function () use ($exam) {
            return $this->getSelfExamQuestion($exam);
        });
        $questions_answer = cache()->get('mcq-exam-questions-answer-'.$testId, []);
        $remaining_time = $this->calculateRemainingMinutes($test,$exam);
        if($remaining_time['is_end']){
            $this->info('Opps Sorry !! This Exam Time out');
        }
        $examSubmitUrl = route('student.self-mcq-exam-submit');
        return view('frontend.student.exam_center.exam_mcq_and_omr', compact('exam','questions','questions_answer','remaining_time','testId','examSubmitUrl'));
    }

    public function selfMcqExamSubmit()
    {
        try {
            $result = $this->processSelfMcqExamSubmit(request()->testId);
            if ($result['status'] === 'success') {
                cache()->forget('self-exam-rendom-questions-'.request()->testId);
                cache()->forget('mcq-exam-questions-answer-'.request()->testId);
                cache()->forget('self-exam-runing-exam-test-id-'.auth()->user()->id);
                return response()->json([
                    'status' => 'success', 'message' => $result['message'],
                    'redirect' => route(
                           'student.result-sheet',[
                           'examType' => 'self', 
                           'testId' => request()->testId]
                    )
                ]);
            }
            return response()->json(['status' => 'error', 'message' => 'An error occurred while submitting the exam.']);
        } catch (\Exception $e) {
            return response()->json(['status' => 'error', 'message' => $e->getMessage()]);
        }
    }

    private function processSelfMcqExamSubmit(int $testId){

        $test= SelfTest::findOrFail($testId);
        $started_time=Carbon::parse($test->created_at);
        $difference=$started_time->diffInSeconds(Carbon::now());
        $difference-=4;
        $exam = cache()->get('academy-self-exam-'.$test->exam_id,[]);

        $questions = cache()->get('self-exam-rendom-questions-'.$testId, []);
        $correctOption = array_column(array_map(fn($item) => [
                'question_id' => $item->question_id,
                'correct_option' => $item->correct_option,
            ],$questions), 'correct_option', 'question_id');

        $selectedOption = cache()->get('mcq-exam-questions-answer-'.$testId, []);

        $number_of_correct=0;
        $number_of_wrong=0;
        $number_of_not_answered=0;

        foreach ($correctOption as $key => $value) {
            if(isset($selectedOption[$key])){
                if($value == $selectedOption[$key]){
                    $number_of_correct++;
                }else{
                    $number_of_wrong++;
                }
            }else{
                $number_of_not_answered++;
            }
        }

        $total_positive_marks=$number_of_correct * $exam->marks_per_question;
        $total_negative_marks=$number_of_wrong * $exam->negative_marks_per_question;
        $test->earned_marks= $total_positive_marks - $total_negative_marks;
        $test->negative_marks=$total_negative_marks;
        $test->positive_marks=$total_positive_marks;
        
        $test->number_of_correct_answers=$number_of_correct;
        $test->number_of_wrong_answers=$number_of_wrong;
        $test->number_of_not_answered=$number_of_not_answered;
        if($test->time_taken_in_second==null){
            $dur=($exam->total_duration * 60);
            if($dur < $difference ){
                $test->time_taken_in_second=$dur;
            }else{
                $test->time_taken_in_second=$difference;
            }
        }
        $test->is_finished=1;
        $test->save();
        $this->insertSelfQuestionAnswer($questions, $selectedOption, $testId);
        
        return [
            'status' => 'success',
            'message' => 'Exam submitted successfully',
            'test_id' => $testId
        ];
    }

    private function insertSelfQuestionAnswer(array $questions,array $selectedOption,int $testId)
    {
        $records = [];
        foreach ($questions as $question) {
            $records[] = [
                'self_test_id' => $testId,
                'question_id' => $question->question_id,
                'subject_id' => $question->subject_id,
                'selected_option' => isset($selectedOption[$question->question_id]) ? $selectedOption[$question->question_id] : '',
                'correct_option' => $question->correct_option,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }
        SelfQuestionAnswer::insert($records);
    }
}
