<?php

namespace App\Http\Controllers\Student;
use App\Models\Test;
use App\Models\SelfTest;
use App\Traits\ToastrMessage;
use Illuminate\Support\Facades\Route;
use App\Models\ExamManagement;
use App\Traits\ExamHelperTrait;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Cache;
use Illuminate\Http\Request;

class ResultSheetController extends Controller
{
    use ToastrMessage;
    
    public function resultSheet($examType,$testId = null) {
        $model = $examType === 'live' ? Test::class : SelfTest::class;
        $query = $model::with(['exam', 'question_answers.question.mcqQuestion'])
            ->where('user_id', auth()->id());
        $last_test = $testId 
            ? $query->where('id', $testId)->latest()->first()
            : $query->latest()->first();

        if($last_test != null){
            $myexam  = $last_test->exam;
            $getIsResultPublished = $last_test->getIsResultPublished() ;
            $getLeadersHigest = $last_test->getLeadersHigest();

            $subejct_base_marks = DB::table('subject_base_marks')->where('test_id',$last_test->id)
                                ->select('subject_base_marks.*','subjects.title','exams.marks_per_question')
                                ->leftJoin('subjects','subject_base_marks.subject_id','subjects.id')
                                ->leftJoin('exams','subject_base_marks.exam_id','exams.id')
                                ->get()->toArray();

            $subejct_base_marks =  count($subejct_base_marks) > 0 ? $subejct_base_marks : [];

            if($last_test->group_exam != null){
                $result = $last_test->group_exam->is_result_published;
            }else{
                $result = null ;
            }
            return view('frontend.student.exam_center.reselt_sheet',compact('last_test','myexam','result','subejct_base_marks','getIsResultPublished','getLeadersHigest'));
        }else{
            return view('frontend.student.exam_center.no_result_found');
        }
    }

    public function meritList(string $examType,int $testId){
        $model = $examType === 'live' ? Test::class : SelfTest::class;
        $test = $model::findOrFail($testId);
        $leadersQuery = $test->getExamQuery();
        $meritLists = $leadersQuery
            ->orderBy('earned_marks', 'desc')
            ->orderBy('time_taken_in_second', 'asc')
            ->paginate(20)->withQueryString();

        return view('frontend.student.exam_center.merit_list',compact('meritLists'));
    }

    public function correctAnsSheet(string $examType,int $testId){
        $model = $examType === 'live' ? Test::class : SelfTest::class;
        $test = $model::with(['exam', 'question_answers.question.mcqQuestion'])
            ->where('user_id', auth()->id())
            ->where('id', $testId)
            ->firstOrFail();

        $exam = $test->exam;
        $question_answers = $test->question_answers;

        return view('frontend.student.exam_center.correct_ans_sheet',compact('test','exam','question_answers'));
    }

    public function questionReport(){
        DB::table('questions_report')->updateOrInsert(
            ['user_id' => auth()->id(), 'question_id' => request('question_id')],
            ['comment' => request('comment'), 'updated_at' => now(), 'created_at' => now()]
        );
    }
}