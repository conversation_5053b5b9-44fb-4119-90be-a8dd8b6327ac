<?php

namespace App\Http\Controllers\Student;

use Carbon\Carbon;
use App\Models\Test;
use App\Models\SelfTest;
use Illuminate\Http\Request;
use App\Traits\ToastrMessage;
use App\Models\ExamManagement;
use App\Http\Controllers\Controller;

class ExamCenterController extends Controller
{
    use ToastrMessage;
    public function examCenter(){
           // Check if any live exams exist in the query results
           $hasLiveExam = ExamManagement::whereDate('exam_management.starts_at', '=', now()->toDateString())
           ->where(function ($q) {
               $q->where('exam_management.exam_time_end', '>=', now())
               ->orWhereNull('exam_management.exam_time_end');
           })
           ->exists();
        return view('frontend.student.exam_center.exam_center',compact('hasLiveExam'));
    }

    public function allExamsList(Request $request){
        $filter = $request->filter ?? '';
        $instructorId = $request->instructor_id;
        $model = $request->model;
        $query = ExamManagement::query()
            ->select([
                'exam_management.*',
                'tests.id as test_attempted_id',
                'tests.is_finished as is_test_finished',
            ])
            ->leftJoin('tests', function ($join) {
                $join->on('exam_management.id', '=', 'tests.exam_id')
                    ->where('tests.user_id', auth()->user()->id);
            })
            ->with('instructor:id,name') // Eager load instructor details
            ->where('exam_management.is_approved', 1)
            ->where('exam_management.model', $model);
        if($request->batch_id){
            $query->join('exam_batches', 'exam_management.id', '=', 'exam_batches.exam_management_id')
                ->where('exam_batches.batch_id', $request->batch_id);
        }
        // Apply filters based on request parameters
        if ($request->e_id) {
            $e_id = customDecrypt($request->e_id);
            $query->where('exam_management.id', $e_id);
        }
        if ($instructorId) {
            $query->where('exam_management.created_by', $instructorId);
        }

          // Apply search filter
        if ($request->filled('search')) {
            $query->leftJoin('users as instructor', 'exam_management.created_by', '=', 'instructor.id');
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('exam_management.title', 'like', "%{$search}%")
                  ->orWhere('instructor.name', 'like', "%{$search}%"); 
            });
        }
        
        // Apply date filter
        if ($request->filled('date')) {
             $date = Carbon::parse($request->date)->format('Y-m-d');
             $query->whereDate('starts_at', $date); // Match the exact date
        }
        // Filtering logic
        switch ($filter) {
            case 'live':
                $query->whereDate('exam_management.starts_at', '=', now()->toDateString())
                    ->where(function ($q) {
                        $q->where('exam_management.exam_time_end', '>=', now())
                            ->orWhereNull('exam_management.exam_time_end');
                    });
                break;

            case 'previous':
                $query->where(function ($q) {
                    $q->whereDate('exam_management.exam_time_end', '<', now())
                        ->orWhere('exam_management.starts_at', '<', now());
                });
                break;

            case 'upcoming':
                $query->whereDate('exam_management.starts_at', '>', now());
                break;
        }
        // Sorting logic: Not attempted first, recent exams next
        $query->orderByRaw('tests.id IS NULL DESC') // Not attempted exams first
            ->orderBy('exam_management.starts_at', 'DESC'); // Recent exams first
        $exams = $query->paginate(10)->withQueryString();
        return view('frontend.student.exam_center.all_exams_list',compact('exams','filter'))->render();
    }

    public function allSelfExams(Request $request){
        $instructorId = $request->instructor_id;

        $query = ExamManagement::with('instructor:id,name')
        ->where('model', 'self')
        ->where('exam_management.is_approved', 1);
        if ($instructorId) {
            $query->where('exam_management.created_by', $instructorId);
        }
        return view('frontend.student.exam_center.all_exams_list',compact('exams','filter'))->render();

    }

    public function myAttendedExam(string $examType){
        $model = $examType === 'live' ? Test::class : SelfTest::class;
        $tests = $model::where('user_id', auth()->id())
        ->where('is_finished',1)
        ->orderBy('id','desc')->get();
        if(count($tests) > 0){
            return view('frontend.student.exam_center.my_attended_exam',compact('tests','examType'));
        }else{
            $this->info('No exam attended yet');
            return redirect()->route('student.dashboard');
        }
    }

    public function redirectExamCenter($model,$examId){
        $encryptExamId = customDecrypt($examId);
        $exam = ExamManagement::select('id','starts_at','exam_time_end')->where('id',$encryptExamId)->first();
        if (!$exam) {
            return response()->json(['status'=>false,'message' => 'Exam not found']);
        }
        $status = $exam->getStatus();
        $url = env('APP_URL') . '/student/exam-center?page=1&filter='.$status.'&model='.$model.'&e_id='.$examId;
        return redirect($url);
    }
}