<?php

namespace App\Http\Controllers\Student;

use Carbon\Carbon;

use App\Models\Test;
use Illuminate\Http\Request;
use App\Traits\ToastrMessage;
use App\Models\ExamManagement;
use App\Models\QuestionAnswer;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Traits\ExamHelperTrait;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Crypt;
use App\Http\Services\Exam\GroupExamServices;

class GroupExamController extends Controller
{
    use ToastrMessage,ExamHelperTrait;

    public function __construct(
        private GroupExamServices $groupExamServices
    ){}

    public function mcqOmrDesign()
    {
        return view('frontend.student.exam_center.mcq_omr_design');
    }

    public function getExam(int $id)
    {
        $key  = 'academy-group-exam-'.$id;
        $exam = $this->getExamById($id, $key);
        $timeLeftInSeconds = $this->calculateExamStartRemainingMinutes($exam);
        if(isset($exam) && $this->checkAllValidationForExam($exam)){
            $isAlreadyEnrolled = $this->isAlreadyEnrolled($exam->id, auth()->id());
            // Assuming $exam->starts_at is in "d-m-Y h:i:s A" format
            $examStartsAt = Carbon::createFromFormat('d-m-Y h:i:s A', $exam->starts_at);
            if ( Carbon::now()->greaterThanOrEqualTo($examStartsAt)) {
                if(!$isAlreadyEnrolled){
                    $this->examEnrolment($exam->id, auth()->id(),$exam->fee);
                }
                return $this->processExam($exam);
            }elseif(!Carbon::now()->greaterThanOrEqualTo($examStartsAt) && !$isAlreadyEnrolled){
                $this->examEnrolment($exam->id, auth()->id(),$exam->fee);
                 return view('frontend.student.exam_center.count_down', compact('timeLeftInSeconds'));
            }else{
                return view('frontend.student.exam_center.count_down', compact('timeLeftInSeconds'));
            }
        }else{
            cache()->forget($key);
            $this->info('This exam is not available');
            return back();
        }
    }

    private function processExam(object $exam){

        $is_tests = $this->isTestRecordExist($exam->id);

        if(!$is_tests){
            $testId = $this->createTestRecord($exam->id, auth()->user()->id);
        }else{
            $testId = $this->getTestRecordId($exam->id);
        }
        if($exam->type_id == 1){
            return redirect()->route('student.start-mcq-exam',['id' => Crypt::encryptString($testId)]);
        }else if($exam->type_id == 2){
            $this->info('This Short question exam module is under development');
        }else{
            $this->info('This CQ exam module is under development');
        }
    }

    // Create a new record in the tests table, capturing test attempt details
    public function createTestRecord(int $examId, int $userId){
        $test_id = Test::create([
            'user_id' => auth()->user()->id,
            'exam_id' =>$examId,
            'group_exam_id' => $examId
        ])->id;
        return $test_id;
    }

    public function startMcqExam($id)
    {
        $enc_id=$id;
        $testId= Crypt::decryptString($id);
        $test= Test::findOrFail($testId);
        $exam = cache()->get('academy-group-exam-'.$test->exam_id);
        $questions = $this->getGroupExamQuestion($exam->id);
        $questions_answer = cache()->get('mcq-exam-questions-answer-'.$testId, []);
        $remaining_time = $this->calculateRemainingMinutes($test,$exam);
        if($remaining_time['is_end']){
            $this->info('Opps Sorry !! This Exam Time out');
        }
        $examSubmitUrl = route('student.mcq-group-exam-submit');
        return view('frontend.student.exam_center.exam_mcq_and_omr', compact('exam','questions','questions_answer','remaining_time','testId','examSubmitUrl'));
    }

    public function mcqQuestionSelectOption()
    {
        $key = 'mcq-exam-questions-answer-'.request()->testId;
        $questions_answer = cache()->get($key, []);
        $questions_answer[request()->question_id] = request()->option;
        cache()->put($key, $questions_answer, 10800);//3 hours
    }

    public function mcqGroupExamSubmit()
    {
        try {
            $result = $this->groupExamServices->mcqGroupExamSubmit(request()->testId);
            if ($result['status'] === 'success') {
                cache()->forget('mcq-exam-questions-answer-'.request()->testId);
                return response()->json([
                     'status' => 'success', 'message' => $result['message'],
                     'redirect' => route(
                            'student.result-sheet',[
                            'examType' => 'live', 
                            'testId' => request()->testId]
                     )
                ]);
            }
            return response()->json(['status' => 'error', 'message' => 'An error occurred while submitting the exam.']);
        } catch (\Exception $e) {
            return response()->json(['status' => 'error', 'message' => $e->getMessage()]);
        }
    }

    private function calculateExamStartRemainingMinutes($exam)
    {
        $carbonDate = Carbon::createFromFormat('d-m-Y h:i:s A', $exam->starts_at);
        // Get the current time as a Carbon instance
        $now = Carbon::now();
        return $now->diffInSeconds($carbonDate);
    }
}