<?php

namespace App\Http\Services\Exam;

use Carbon\Carbon;
use App\Models\Test;
use App\Models\QuestionAnswer;
use Illuminate\Support\Facades\DB;
use App\Traits\ExamHelperTrait;


class GroupExamServices
{
    use ExamHelperTrait;

    public function mcqGroupExamSubmit(int $testId){

        $test=Test::findOrFail($testId);

        if($test->is_finished==0){
            $started_time=Carbon::parse($test->created_at);
            $difference=$started_time->diffInSeconds(Carbon::now());
            $difference-=4;
            $exam = cache()->get('academy-group-exam-'.$test->exam_id,[]);
            // $exam = $test->exam;

            $questions = $this->getGroupExamQuestion($exam->id);
            $correctOption = array_column(array_map(fn($item) => [
                    'question_id' => $item->question_id,
                    'correct_option' => $item->correct_option,
                ],$questions), 'correct_option', 'question_id');
            
            $selectedOption = cache()->get('mcq-exam-questions-answer-'.$testId, []);

            $number_of_correct=0;
            $number_of_wrong=0;
            $number_of_not_answered=0;

            foreach ($correctOption as $key => $value) {
                if(isset($selectedOption[$key])){
                    if($value == $selectedOption[$key]){
                        $number_of_correct++;
                    }else{
                        $number_of_wrong++;
                    }
                }else{
                    $number_of_not_answered++;
                }
            }

         
            $total_positive_marks=$number_of_correct * $exam->marks_per_question;
            $total_negative_marks=$number_of_wrong * $exam->negative_marks_per_question;
            $test->earned_marks= $total_positive_marks - $total_negative_marks;
            $test->negative_marks=$total_negative_marks;
            $test->positive_marks=$total_positive_marks;
            
            $test->number_of_correct_answers=$number_of_correct;
            $test->number_of_wrong_answers=$number_of_wrong;
            $test->number_of_not_answered=$number_of_not_answered;
            if($test->time_taken_in_second==null){
                $dur=($exam->total_duration * 60);
                if($dur < $difference ){
                    $test->time_taken_in_second=$dur;
                }else{
                    $test->time_taken_in_second=$difference;
                }
            }
            $test->is_finished=1;
            $test->save();
            $this->insertQuestionAnswer($questions, $selectedOption, $testId);

            $this->subjectBaseMarksCalculation($testId, $test,$exam);
        }
        return [
            'status' => 'success',
            'message' => 'Exam submitted successfully',
            'test_id' => $testId
        ];
    }

    public function insertQuestionAnswer(array $questions,array $selectedOption,int $testId)
    {
        $records = [];
        foreach ($questions as $question) {
            $records[] = [
                'test_id' => $testId,
                'question_id' => $question->question_id,
                'subject_id' => $question->subject_id,
                'selected_option' => isset($selectedOption[$question->question_id]) ? $selectedOption[$question->question_id] : '',
                'correct_option' => $question->correct_option,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        QuestionAnswer::insert($records);
    }

    private function subjectBaseMarksCalculation(int $testid,object $test,object $exam): void
    {
     
        date_default_timezone_set('Asia/Dhaka');
        $time = date('Y-m-d H:i:s');
        $group_exam_id = $test->group_exam_id != null ?  $test->group_exam_id : null;
        $qusansArray = [];
        $allQuestionData = DB::table('question_answers')
                            ->where('test_id',$testid)->get()
                            ->groupBy('subject_id');
        $total_array = [];
        foreach($allQuestionData as $key => $answers){
            $number_of_correct = 0;
            $number_of_not_answered = 0;
            $number_of_wrong = 0;
            foreach($answers as $answer){
                $subject_base_question_count = count($answers);
                if($answer->selected_option!=null){
                    if($answer->selected_option ==  $answer->correct_option){
                        $number_of_correct++;
                    }else{
                        $number_of_wrong++;
                    } 
                }else{
                    $number_of_not_answered++;
                }
            }
            $total_positive_marks=$number_of_correct * $exam->marks_per_question;
            $total_negative_marks=$number_of_wrong * $exam->negative_marks_per_question;
            $earned_marks= $total_positive_marks - $total_negative_marks;

            $total_array[$key] = [
                'subject_id'  => $key,
                'correct_answer' => $number_of_correct,
                'wrong_answer'  => $number_of_wrong,
                'not_answer'   => $number_of_not_answered,
                'positive_marks' => $total_positive_marks,
                'negative_marks' => $total_negative_marks,
                'earned_marks'   => $earned_marks,
                'test_id' => $testid,
                'subject_base_question' => $subject_base_question_count
            ];
        }
        $insertData = [];
        foreach ($total_array as $key => $single_array) {
            $insertData[] = [
                'test_id' => $testid,
                'subject_id'  => $key,
                'user_id' => $test->user_id,
                'exam_id' => $exam->id,
                'group_exam_id' => $group_exam_id != null ? $group_exam_id : null,
                'correct_answer' => $single_array['correct_answer'],
                'wrong_answer'  => $single_array['wrong_answer'],
                'not_answer'   => $single_array['not_answer'],
                'positive_marks' => $single_array['positive_marks'],
                'negative_marks' => $single_array['negative_marks'],
                'earned_marks'   => $single_array['earned_marks'],
                'subject_base_question'   => $single_array['subject_base_question'],
                'created_at' => $time,
                'updated_at' => $time,
            ];
        }
        if (!empty($insertData)) {
            DB::table('subject_base_marks')->insert($insertData);
        }
        return ;
    }
}