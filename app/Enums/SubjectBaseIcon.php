<?php

namespace App\Enums;

class SubjectBaseIcon
{
    // Subject Base Icons
    const SSC = 'fas fa-school';
    const HSC = 'fas fa-university';
    const MEDICAL_ADMISSION = 'fas fa-stethoscope';
    const ENGINEERING_ADMISSION = 'fas fa-cogs';
    const UNIVERSITY_ADMISSION = 'fas fa-graduation-cap';
    const IELTS = 'fas fa-language';
    const BCS = 'fas fa-gavel';
    const PRIMARY = 'fas fa-chalkboard-teacher';
    const BANK = 'fas fa-building';
    const TEACHERS_REGISTRATION = 'fas fa-id-card';
    const NINTH_GRADE_NON_CADRE = 'fas fa-user-graduate';
    const OTHERS = 'fas fa-ellipsis-h';
    const MEDICAL = 'fas fa-hospital';

    /**
     * Get icon by key (numeric).
     *
     * @param int $key
     * @return string|null
     */
    public static function getIconByKey(int $key)
    {
        $icons = [
            1 => self::SSC,
            2 => self::HSC,
            3 => self::MEDICAL_ADMISSION,
            4 => self::ENGINEERING_ADMISSION,
            5 => self::UNIVERSITY_ADMISSION,
            6 => self::IELTS,
            7 => self::BCS,
            8 => self::PRIMARY,
            9 => self::BANK,
            10 => self::TEACHERS_REGISTRATION,
            11 => self::NINTH_GRADE_NON_CADRE,
            12 => self::OTHERS,
            13 => self::MEDICAL,
        ];

        return $icons[(int)$key] ?? null; // Return icon if exists, else return null.
    }

    /**
     * Get label for a specific icon.
     *
     * @param string $icon
     * @return string
     */
    public static function getLabel($icon)
    {
        switch ($icon) {
            case self::SSC:
                return 'SSC';
            case self::HSC:
                return 'HSC';
            case self::MEDICAL_ADMISSION:
                return 'Medical Admission';
            case self::ENGINEERING_ADMISSION:
                return 'Engineering Admission';
            case self::UNIVERSITY_ADMISSION:
                return 'University Admission';
            case self::IELTS:
                return 'IELTS';
            case self::BCS:
                return 'BCS';
            case self::PRIMARY:
                return 'Primary';
            case self::BANK:
                return 'Bank';
            case self::TEACHERS_REGISTRATION:
                return 'Teachers Registration';
            case self::NINTH_GRADE_NON_CADRE:
                return '9th Grade Non Cadre';
            case self::OTHERS:
                return 'Others';
            case self::MEDICAL:
                return 'Medical';
            default:
                return 'Unknown';
        }
    }
}
