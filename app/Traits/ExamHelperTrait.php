<?php

namespace App\Traits;

use Carbon\Carbon;
use App\Models\Test;
use App\Models\ExamManagement;
use App\Models\QuestionAnswer;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use App\Models\ExamEnrolment;

trait ExamHelperTrait
{
    public function isStudent(){
        $user = auth()->user();
        return $user->role == USER_ROLE_STUDENT && $user->student->status == STATUS_APPROVED;
    }
    /**
     * Check if the student has enough credits/balance to enroll in the exam.
     */
    public function hasSufficientCredits(int $userId, int $requiredCredits): bool
    {
        $studentCredits = DB::table('users')
            ->where('id', $userId)
            ->value('balance');

        return $studentCredits >= $requiredCredits;
    }
     /**
     * Check if the student is already enrolled in the exam.
     */
    public function isAlreadyEnrolled(int $examId, int $userId): bool
    {
        return DB::table('exam_enrolments')
            ->where('exam_id', $examId)
            ->where('user_id', $userId)
            ->exists();
    }

    public function isPurchaseInstructorPackage(int $instructorId): bool
    {
        // return DB::table('instructor_packages')
        //     ->where('instructor_id', $instructorId)
        //     ->exists();
        return true;
    }
    /**
     * Deduct the enrollment credits from the student's account.
     */
    public function deductCredits(int $userId, int $credits): void
    {
        DB::table('users')
            ->where('id', $userId)
            ->decrement('balance', $credits);
    }

    public function isTestRecordExist(int $examId){
        return Test::where('user_id', auth()->user()->id)
            ->where('exam_id', $examId)
            ->exists();
    }

    public function getTestRecordId(int $examId){
        return DB::table('tests')->where('user_id',auth()->user()->id)
            ->where('exam_id',$examId)
            ->where('group_exam_id',$examId)
            ->first()->id;
    }

    public function isSelfTestRecordExist(){
        $isExistRuningTestId = cache()->get('self-exam-runing-exam-test-id-'.auth()->user()->id);
        return $isExistRuningTestId ? $isExistRuningTestId : false;
    }

    public function getExamById(int $examId,string $key){
        return Cache::remember($key, 10800, function () use ($examId) {
            return ExamManagement::where('is_approved', 1)
            ->where('id', $examId)->first();
        });
    }

    public function examEnrolment(int $examId, int $userId, int $fee): void
    {
        ExamEnrolment::create([
            'exam_id' => $examId,
            'user_id' => $userId,
            'enrolment_date' => now(),
        ]);
        $this->deductCredits($userId, $fee);
        $this->success('You have successfully enrolled in the exam');
    }

    public function checkAllValidationForExam(object $exam)
    {
        if(!$this->isStudent()){
            $this->error('You are not authorized to access this page');
            return false;
        }
        if(!$this->hasSufficientCredits(auth()->id(), $exam->fee)){
            $this->error('You do not have enough credits to enroll in the exam');
            return false;
        }
        if(!$this->isPurchaseInstructorPackage($exam->created_by)){
            $this->error('You have not purchased the instructor package');
            return false;
        }
        return true;
    }

    public function getGroupExamQuestion(int $group_exam_id): array
    {
        return Cache::remember('academy_group_exam_questions'.$group_exam_id, 1440, function () use ($group_exam_id) {
            return DB::table('academy_group_exam_questions')
                    ->select(
                        'academy_group_exam_questions.question_id',
                        'academy_questions.question_text',
                        'academy_questions.academy_type',
                        'mcq_questions.option_1',
                        'mcq_questions.option_2',
                        'mcq_questions.option_3',
                        'mcq_questions.option_4',
                        'mcq_questions.option_5',
                        'mcq_questions.correct_option',
                        'academy_questions.subject_id'
                    )
                    ->where('academy_group_exam_questions.group_id',$group_exam_id)
                    ->leftJoin('academy_questions','academy_group_exam_questions.question_id','academy_questions.id')
                    ->leftJoin('mcq_questions','academy_group_exam_questions.question_id','mcq_questions.question_id')
                    ->get()->toArray();
        });
    }

    public function getSelfExamQuestion(object $exam): array
    {
        $allQuestions =  Cache::remember('academy_self_exam_questions'.$exam->id, 1440, function () use ($exam) {
            $examManagementConfigurationData = DB::table('exam_management_configurations')
            ->where('exam_management_id', $exam->id)
            ->join('exam_management_configuration_subjects', 'exam_management_configuration_subjects.exam_management_configurations_id', '=', 'exam_management_configurations.id')
            ->join('exam_management_configuration_subject_chapters', 'exam_management_configuration_subjects.id', '=', 'exam_management_configuration_subject_chapters.c_subject_id')
            ->select('exam_management_configuration_subjects.subject_id', 'exam_management_configuration_subject_chapters.chapter_id')
            ->get();
    
            // Extract the necessary collections
            $allSubjectIds = $examManagementConfigurationData->pluck('subject_id')->unique()->toArray();
            $allChapterIds = $examManagementConfigurationData->pluck('chapter_id')->unique()->toArray();
    
            // Fetch academy type IDs
            $examManagementAcademyTypes = DB::table('exam_management_academy_type')
                ->where('exam_management_id', $exam->id)
                ->pluck('academy_type_id')
                ->toArray();
    
            // Build the base query for questions
            $allQuestionsQuery = DB::table('academy_questions')
                ->whereIn('academy_questions.chapter_id', $allChapterIds)
                ->whereIn('academy_questions.subject_id', $allSubjectIds)
                ->whereIn('academy_questions.academy_type', $examManagementAcademyTypes);
                // Check question type for MCQ or other types
            if ($exam->type_id == 1) {
                $allQuestions = $allQuestionsQuery
                    ->join('mcq_questions', 'mcq_questions.question_id', '=', 'academy_questions.id')
                    ->join('subjects','academy_questions.subject_id','subjects.id')
                    ->join('chapters','academy_questions.chapter_id','chapters.id')
                    ->select(
                        'academy_questions.id as question_id',
                        'academy_questions.question_text',
                        'academy_questions.academy_type',
                        'academy_questions.subject_id',
                        'mcq_questions.option_1',
                        'mcq_questions.option_2',
                        'mcq_questions.option_3',
                        'mcq_questions.option_4',
                        'mcq_questions.option_5',
                        'mcq_questions.correct_option',
                        // 'subjects.title as subject_name',
                        // 'academy_questions.chapter_id',
                        // 'chapters.title as chapter_name',
                    );
                    
            } else {
                $allQuestions = $allQuestionsQuery
                    ->select(
                        'academy_questions.id',
                        'academy_questions.question_text',
                        'academy_questions.hint',
                        'academy_questions.solution',
                        'academy_questions.question_detail',
                        'academy_questions.subject_id',
                        'academy_questions.chapter_id'
                    );
            }
            return $allQuestions->get();
        });
        return $allQuestions->random($exam->number_of_question)->toArray();
    }



    function calculateRemainingMinutes(object $test,object $exam)
    {
        // Current time in 'Asia/Dhaka' timezone
        $now = Carbon::now('Asia/Dhaka');
        
        // Parse the exam start time and convert to 'Asia/Dhaka' timezone
        $start_time = Carbon::parse($test->created_at)->setTimezone('Asia/Dhaka');
        
        // Total duration of the exam in seconds (total_duration is given in minutes)
        $exam_duration_seconds = $exam->total_duration * 60; // Convert minutes to seconds
        
        // Calculate the time difference between the current time and the exam start time
        $time_elapsed = $start_time->diffInSeconds($now);
    
        // Default remaining time is based on the total duration minus elapsed time
        $remaining_seconds = $exam_duration_seconds - $time_elapsed;

        // If there is an explicit exam end time, use it to limit the remaining time
        if ($exam->exam_time_end) {
            $end_time = Carbon::parse($exam->exam_time_end)->setTimezone('Asia/Dhaka');
            
            // If the exam end time is earlier than the current time, set remaining time to 0
            if ($now->greaterThanOrEqualTo($end_time)) {
                $remaining_seconds = 0;
            } else {
                // Calculate remaining seconds based on the exam end time
                $remaining_seconds = min($remaining_seconds, $end_time->diffInSeconds($now));
            }
        }
        // Convert the remaining time to minutes
        $remaining_minutes = $remaining_seconds / 60;
        // Ensure remaining time is not negative
        return [
            'time'=> max(0, round($remaining_minutes)),
            'is_end'=> $remaining_seconds <= 0 ? true : false
        ];
    }
}