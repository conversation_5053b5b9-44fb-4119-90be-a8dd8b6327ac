<?php

namespace App\Traits;

trait ToastrMessage
{
    public function success($message)
    {
        return toastr()->success($message, '', ["positionClass" => "toast-bottom-right"]);
    }

    public function error($message)
    {
        return toastr()->error($message, '', ["positionClass" => "toast-bottom-right"]);
    }

    public function warning($message)
    {
        return toastr()->warning($message, '', ["positionClass" => "toast-bottom-right"]);
    }

    public function info($message)
    {
        return toastr()->info($message, '', ["positionClass" => "toast-bottom-right"]);
    }
}