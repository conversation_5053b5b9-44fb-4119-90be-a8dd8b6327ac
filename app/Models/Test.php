<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Test extends Model
{
    use HasFactory;
    protected $fillable=[
        'user_id',
        'exam_id',
        'group_exam_id',
        'earned_marks',
        'negative_marks',
        'positive_marks',
        'time_taken_in_second',
        'number_of_correct_answers',
        'number_of_wrong_answers',
        'number_of_not_answered',
        'is_finished',
    ];
    public function user(){
        return $this->belongsTo('App\Models\User','user_id');
    }

    public function exam(){
        return $this->belongsTo(ExamManagement::class, 'exam_id');
    }

    public function question_answers(){
        return $this->hasMany('App\Models\QuestionAnswer','test_id');
    }

    public function getExamQuery()
    {
        // Check if the exam exists
        if ($this->exam) {
            return $this->group_exam_id === null
                ? $this->exam->tests()->whereNull('group_exam_id')
                : $this->exam->tests()->whereNotNull('group_exam_id');  
        }

        // If there is no exam, return an empty query builder
        return Test::query()->whereRaw('1 = 0'); // This returns an empty query builder
    }

    public function getRanking()
    {
        $tests = $this->getExamQuery()->orderBy('earned_marks', 'desc')->orderBy('time_taken_in_second', 'asc')->get();
        // If there are no tests, return a default ranking
        if ($tests->isEmpty()) {
            return [
                'rank' => 0,
                'total' => 0
            ];
        }
        $rank = 1;
        foreach ($tests as $test) {
            if ($test->id == $this->id) {
                break;
            }
            $rank++;
        }
        return [
            'rank' => $rank,
            'total' => $tests->count() // Use the count method on the collection
        ];
    }
    
    public function getLeadersHigest()
    {
       return $this->getExamQuery()
            ->orderBy('earned_marks','desc')
            ->orderBy('time_taken_in_second','asc')
            ->first('earned_marks');
    }

    public function getIsResultPublished(){
        if($this->group_exam_id==null){
            return true;
        }else{
            // return $this->group_exam->is_result_published==1;
            return true;
        }
    }

}
