<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class QuestionAnswer extends Model
{
    use HasFactory;

    protected $fillable=[
        'test_id',
        'question_id',
        'selected_option',
        'answer',
        'correct_option',
        'subject_id',
    ];
    public function question(){
        return $this->belongsTo('App\Models\AcademyQuestion','question_id');
    }
    public function test(){
        return $this->belongsTo('App\Models\Test','test_id');
    }
    public function isCorrect(){
        if($this->correct_option==$this->selected_option){
            return true;
        }else{
            return false;
        }
    }
}
