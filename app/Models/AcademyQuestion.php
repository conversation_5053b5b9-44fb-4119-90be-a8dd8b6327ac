<?php

namespace App\Models;

use App\Models\Chapter;
use App\Models\Subject;
use App\Models\McqQuestion;
use App\Models\QuestionType;
use App\Models\QuestionHistory;
use App\Models\QuestionsBoardHistory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;

class AcademyQuestion extends Model
{
    use HasFactory;

    protected $fillable = [
        'chapter_id', 'subject_id', 'question_type_id', 'difficulty_level',
        'marks', 'question_text', 'hint', 'solution', 'question_detail',
        'reference_id', 'academy_type', 'created_by',
    ];

    public function subject(): HasOne
    {
        return $this->hasOne(Subject::class, 'id', 'subject_id');
    }

    public function chapter(): HasOne
    {
        return $this->hasOne(Chapter::class, 'id', 'chapter_id');
    }

    public function topic():HasOne
    {
        return $this->hasOne(Topic::class, 'id', 'topic_id');
    }

    public function academyType(): HasOne
    {
        return $this->hasOne(AcademyType::class, 'id','academy_type');
    }

    public function questionType(): HasOne
    {
        return $this->hasOne(QuestionType::class, 'id', 'question_type_id');
    }

    public function mcqQuestion(): HasOne
    {
        return $this->hasOne(McqQuestion::class, 'question_id', 'id');
    }

    public function questionHistorie(): HasMany // hasmany
    {
        return $this->hasMany(QuestionHistory::class, 'question_id', 'id');
    }

    public function questionsBoardHistory(): HasMany
    {
        return $this->hasMany(QuestionsBoardHistory::class, 'question_id', 'id');
    }
}
