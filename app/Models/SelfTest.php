<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SelfTest extends Model
{
    use HasFactory;
    protected $table = 'self_tests';
    protected $fillable=[
        'user_id',
        'exam_id',
        'earned_marks',
        'negative_marks',
        'positive_marks',
        'time_taken_in_second',
        'number_of_correct_answers',
        'number_of_wrong_answers',
        'number_of_not_answered',
        'is_finished',
    ];
    public function user(){
        return $this->belongsTo('App\Models\User','user_id');
    }

    public function exam(){
        return $this->belongsTo(ExamManagement::class, 'exam_id');
    }

    public function question_answers(){
        return $this->hasMany('App\Models\SelfQuestionAnswer','self_test_id');
    }

    public function getExamQuery()
    {
        return $this->exam->selfTests();
    }

    public function getRanking(){
      
        $tests =  $this->getExamQuery()->orderBy('earned_marks','desc')->orderBy('time_taken_in_second','asc')->get();

        $rank=1;
        foreach($tests as $test){
            if($test->id==$this->id){
                break;
            }
            $rank++;
        }
        return [
            'rank'=>$rank,
            'total'=>count($tests) ?? 0
        ];
    }
    
    public function getLeadersHigest()
    {
       return $this->getExamQuery()
            ->orderBy('earned_marks','desc')
            ->orderBy('time_taken_in_second','asc')
            ->first('earned_marks');
    }

    public function getIsResultPublished(){
        if($this->group_exam_id==null){
            return true;
        }else{
            // return $this->group_exam->is_result_published==1;
            return true;
        }
    }

}
