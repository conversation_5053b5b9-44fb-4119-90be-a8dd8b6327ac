<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Subject extends Model
{
    use HasFactory;

    protected $table = 'subjects';
    
    protected $fillable = [
        'title',
        'academy_type',
        'created_by',
        'status',
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function academyType()
    {
        return $this->belongsTo(AcademyType::class, 'academy_type');
    }

}
