<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SelfExam extends Model
{
    use HasFactory;

    protected $table = 'self_exams';
    
    protected $fillable=[
        'self_exam_title',
        'institute_id',
        'user_id',
        'subject_id',
        'chapter_id',
        'year_id',
        'unit',
        'number_of_question',
        'marks_per_question',
        'negative_marks_per_question',
        'total_duration',
        'created_by',
        'status',
        'is_finished'
    ];
}
