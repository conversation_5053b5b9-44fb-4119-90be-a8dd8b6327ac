<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Batch extends Model
{
    use HasFactory;

    protected $fillable = ['title','instructor_id','status','code'];

    public function instructor()
    {
        return $this->belongsTo(User::class, 'instructor_id','id');
    }

    public function examManagements()
    {
        return $this->belongsToMany(ExamManagement::class, 'exam_batches', 'batch_id', 'exam_management_id');
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($batch) {
            $batch->instructor_id = auth()->user()->id;
        });
    }
    
}
