<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ExamBatch extends Model
{
    use HasFactory;

    protected $table = 'exam_batches'; // Ensure correct table name

    protected $fillable = [
        'exam_management_id',
        'batch_id',
    ];

    // Relationship with ExamManagement
    public function examManagement()
    {
        return $this->belongsTo(ExamManagement::class, 'exam_management_id');
    }

    // Relationship with Batch
    public function batch()
    {
        return $this->belongsTo(Batch::class, 'batch_id');
    }

}
