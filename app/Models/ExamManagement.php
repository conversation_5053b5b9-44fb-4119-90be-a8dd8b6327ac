<?php

namespace App\Models;

use Carbon\Carbon;
use App\Models\Batch;
use App\Traits\DateFormatTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ExamManagement extends Model
{
    use HasFactory,DateFormatTrait;

    protected $table = 'exam_management';

    protected $fillable = [
        'exam_configuration_id',
        'institute_id',
        'unit_id',
        'type_id',
        'title',
        'fee',
        'total_duration',
        'number_of_question',
        'marks_per_question',
        'negative_marks_per_question',
        'model',
        'result_type',
        'exam_type_id',
        'extra_time',
        'slug',
        'status',
        'created_by',
        'starts_at',
        'is_completed',
        'is_seen',
        'is_approved',
        'approved_at',
        'approved_by',
        'is_result_published',
        'exam_time_end',
        'group_exam_slug',
        'is_expire_date',
    ];

    public function tests(){
        return $this->hasMany(Test::class, 'exam_id');
    }

    public function selfTests(){
        return $this->hasMany(SelfTest::class, 'exam_id');
    }

    public function academyTypes()
    {
        return $this->hasMany(ExamManagementAcademyType::class, 'exam_management_id');
    }

    public function instructor()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function getStatus()
    {
        // Get the current time
        $now = now(); 

        // Parse the `starts_at` string to a Carbon instance
        $startsAt = Carbon::createFromFormat('d-m-Y h:i:s A', $this->starts_at);

        // Parse the `exam_time_end` if it's not null
        $examTimeEnd = $this->exam_time_end ? Carbon::parse($this->exam_time_end) : null;

        // If the exam is today and either the `exam_time_end` is null or it's ongoing
        if ($startsAt->toDateString() === $now->toDateString()) {
            // Live: Same day as `starts_at` and still ongoing or has no defined end time
            if ($examTimeEnd === null || $examTimeEnd->greaterThanOrEqualTo($now)) {
                return 'live';
            }
        } elseif ($startsAt->lessThan($now)) {
            // Previous: Exam has started and either ended or started in the past
            if ($examTimeEnd !== null && $examTimeEnd->lessThan($now)) {
                return 'previous';
            }
        } elseif ($startsAt->greaterThan($now)) {
            // Upcoming: Exam starts in the future
            return 'upcoming';
        }
        // Default to 'previous' if no conditions match (fallback)
        return 'previous';
    }

    public function batches()
    {
        return $this->belongsToMany(Batch::class, 'exam_batches', 'exam_management_id', 'batch_id');
    }
}
