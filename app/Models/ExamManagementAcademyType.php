<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ExamManagementAcademyType extends Model
{
    use HasFactory;

    protected $table = 'exam_management_academy_type';

    protected $fillable = [
        'academy_type_id',
        'exam_management_id',
    ];

    public function examManagement()
    {
        return $this->belongsTo(ExamManagement::class, 'exam_management_id');
    }
}
