<?php

namespace App\Models;

use App\Models\Institute;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasOne;

class QuestionHistory extends Model
{
    use HasFactory;

    protected $fillable=['institute_id','unit_id','question_id','years'];

    public function institutes(): HasOne
    {
        return $this->hasOne(Institute::class, 'id', 'institute_id');
    }
}
