<?php

namespace App\Models;

use Carbon\Carbon;
use App\Traits\ToastrMessage;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class RoutineUpload extends Model
{
    use HasFactory;

    protected $table = 'routine';

    protected $fillable = ['title','academic_type','instructor_id', 'course_id', 'file_path','status'];

    public function instructor()
    {
        return $this->belongsTo(User::class, 'instructor_id');
    }

    public function course()
    {
        return $this->belongsTo(Course::class, 'course_id');
    }

    public function academyType()
    {
        return $this->belongsTo(AcademyType::class, 'academic_type');
    }

    public function getCreatedAtAttribute($value)
    {
        return Carbon::parse($value)->format('d-m-Y');
    }

}
