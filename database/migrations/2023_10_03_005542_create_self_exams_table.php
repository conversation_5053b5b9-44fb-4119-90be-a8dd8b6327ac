<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('self_exams', function (Blueprint $table) {
            $table->id()->index();
            $table->string('self_exam_title', 256)->charset('utf8mb4');
            $table->bigInteger('user_id')->index();
            $table->bigInteger('institute_id')->nullable();
            $table->bigInteger('subject_id');
            $table->bigInteger('chapter_id');
            $table->bigInteger('year_id')->nullable();
            $table->bigInteger('unit')->nullable();
            $table->double('total_duration', 8, 2);
            $table->integer('number_of_question');
            $table->integer('marks_per_question');
            $table->float('negative_marks_per_question');
            $table->integer('status')->nullable();
            $table->tinyInteger('is_finished')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('self_exams');
    }
};
