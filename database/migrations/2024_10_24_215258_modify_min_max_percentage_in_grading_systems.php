<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('grading_systems', function (Blueprint $table) {
            // Modify the 'min_percentage' and 'max_percentage' columns to DECIMAL(5,2)
            $table->decimal('min_percentage', 5, 2)->change();
            $table->decimal('max_percentage', 5, 2)->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('grading_systems', function (Blueprint $table) {
            // Revert the changes back to varchar if needed
            $table->string('min_percentage', 191)->change();
            $table->string('max_percentage', 191)->change();
        });
    }
};
