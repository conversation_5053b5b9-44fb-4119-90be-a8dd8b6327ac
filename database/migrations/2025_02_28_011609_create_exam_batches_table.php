<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('exam_batches', function (Blueprint $table) {
            $table->id();
            $table->foreignId('exam_management_id')->constrained('exam_management')->onDelete('cascade'); // Adjust if necessary
            $table->foreignId('batch_id')->constrained('batches')->onDelete('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('exam_batches', function (Blueprint $table) {
            Schema::dropIfExists('exam_batches');
        });
    }
};
