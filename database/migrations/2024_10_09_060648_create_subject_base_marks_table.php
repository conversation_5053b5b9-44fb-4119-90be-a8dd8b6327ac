<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('subject_base_marks', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->bigInteger('test_id')->nullable(false);
            $table->bigInteger('user_id')->nullable(false);
            $table->bigInteger('subject_id')->nullable(false);
            $table->integer('correct_answer')->nullable();
            $table->integer('wrong_answer')->nullable();
            $table->integer('not_answer')->nullable();
            $table->decimal('positive_marks', 10, 0)->nullable();
            $table->double('negative_marks', 10, 2)->nullable();
            $table->double('earned_marks', 10, 2)->nullable();
            $table->bigInteger('exam_id')->nullable(false);
            $table->bigInteger('group_exam_id')->nullable();
            $table->string('subject_base_question', 191)->nullable();
            $table->timestamp('created_at')->useCurrent();
            $table->timestamp('updated_at')->useCurrent();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('subject_base_marks');
    }
};
