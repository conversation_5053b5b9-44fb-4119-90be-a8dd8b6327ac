<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('academy_group_exam_questions', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('group_id');
            $table->bigInteger('subject_id');
            $table->bigInteger('chapter_id');
            $table->bigInteger('question_id');
            $table->timestamps();
            // Composite unique constraint on group_id and question_id
            $table->unique(['group_id', 'question_id']);
        });
       
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('academy_group_exam_questions');
    }
};
