<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('exam_management', function (Blueprint $table) {

            $table->id();
            $table->string('title');
            $table->unsignedBigInteger('institute_id')->nullable();
            $table->unsignedBigInteger('unit_id')->nullable();
            $table->integer('type_id');
            $table->double('fee', 8, 2);
            $table->integer('total_duration');
            $table->integer('number_of_question');
            $table->double('marks_per_question', 8, 2);
            $table->double('negative_marks_per_question', 8, 2);
            $table->enum('model', ['live', 'self']);
            $table->enum('result_type', ['auto', 'manual']);
            $table->integer('exam_type_id');
            $table->string('extra_time')->nullable();
            $table->string('slug', 256);
            $table->tinyInteger('status')->default(1);
            $table->unsignedBigInteger('created_by');
            $table->dateTime('starts_at');
            $table->tinyInteger('is_completed')->default(0);
            $table->tinyInteger('is_seen')->default(0);
            $table->tinyInteger('is_approved')->default(0);
            $table->dateTime('approved_at')->nullable();
            $table->unsignedBigInteger('approved_by')->nullable();
            $table->tinyInteger('is_result_published')->default(0);
            $table->dateTime('exam_time_end')->nullable();
            $table->tinyInteger('is_expire_date')->nullable();
            $table->tinyInteger('org_id')->nullable();
            $table->timestamps();  // This will create 'created_at' and 'updated_at' fields

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('exam_management');
    }
};
