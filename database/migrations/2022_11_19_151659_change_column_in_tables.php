<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('instructor_certificates', function (Blueprint $table) {
            $table->unsignedBigInteger('instructor_id')->nullable()->change();
        });
       
        Schema::table('instructor_awards', function (Blueprint $table) {
            $table->unsignedBigInteger('instructor_id')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
    }
};
