<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('exam_management_academy_type', function (Blueprint $table) {

            $table->foreignId('academy_type_id')->constrained('academy_types')->onDelete('cascade');
            $table->foreignId('exam_management_id')->constrained('exam_management')->onDelete('cascade');
            
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('exam_management_academy_type');
    }
};
