<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('self_tests', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->bigInteger('user_id')->nullable(false);
            $table->bigInteger('exam_id')->nullable(false);
            $table->double('earned_marks', 8, 2)->nullable();
            $table->double('negative_marks', 8, 2)->nullable();
            $table->double('positive_marks', 8, 2)->nullable();
            $table->double('time_taken_in_second', 8, 2)->nullable();
            $table->integer('number_of_correct_answers')->nullable();
            $table->integer('number_of_wrong_answers')->nullable();
            $table->integer('number_of_not_answered')->nullable();
            $table->tinyInteger('is_finished')->nullable(false)->default(0);
            $table->timestamp('created_at')->nullable();
            $table->timestamp('updated_at')->nullable();

            // Create an index on user_id, exam_id, and id
            $table->index(['user_id', 'exam_id', 'id']);
        });

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('self_tests');
    }
};
