<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('exam_management_configuration_subjects', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('exam_management_configurations_id');
            $table->string('subject_id');
            $table->string('min_difficulty');
            $table->string('max_difficulty');
            $table->string('created_by');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('exam_management_configuration_subjects');
    }
};
