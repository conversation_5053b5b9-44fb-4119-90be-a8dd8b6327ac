<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('result_grading_process', function (Blueprint $table) {
            $table->id();
            $table->integer('academy_type_id')->nullable();
            $table->integer('exam_management_id')->nullable();
            $table->tinyInteger('type');
            $table->integer('cut_marks')->nullable();
            $table->integer('pass_percentage')->nullable();
            $table->string('created_by');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('result_grading_process');
    }
};
