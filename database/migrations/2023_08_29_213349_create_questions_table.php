<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('academy_questions', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('chapter_id');
            $table->unsignedBigInteger('subject_id');
            $table->unsignedBigInteger('question_type_id');
            $table->integer('difficulty_level');
            $table->integer('marks');
            $table->text('question_text');
            $table->text('hint')->nullable();
            $table->text('solution')->nullable();
            $table->text('question_detail')->nullable();
            $table->unsignedBigInteger('reference_id')->nullable();
            $table->integer('academy_type');
            $table->unsignedBigInteger('created_by');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('questions');
    }
};
