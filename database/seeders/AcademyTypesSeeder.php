<?php
// database/seeders/AcademyTypesSeeder.php
namespace Database\Seeders;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class AcademyTypesSeeder extends Seeder
{
    public function run()
    {
        $academyTypes = [
            ['title' => 'SSC'],
            ['title' => 'HSC'],
            ['title' => 'Medical Admission'],
            ['title' => 'Engineering Admission'],
            ['title' => 'University Admission'],
            ['title' => 'BCS'],
            ['title' => 'Primary'],
            ['title' => 'Bank'],
            ['title' => 'Teachers Registration'],
            ['title' => '9th Grade Non Cadre'],
            ['title' => 'IELTS'],
            ['title' => 'Others'],
            ['title' => 'Medical'],
        ];

        DB::table('academy_types')->insertOrIgnore($academyTypes);
    }
}
