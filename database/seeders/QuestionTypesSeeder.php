<?php

namespace Database\Seeders;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class QuestionTypesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $array = [
            [
                'title' => 'MCQ',
                'created_at' => '2023-04-15 05:52:09',
                'updated_at' => '2023-04-15 05:52:09',
            ],
            [
                'title' => 'Short Question',
                'created_at' => '2023-04-15 05:52:36',
                'updated_at' => '2023-04-15 05:52:36',
            ],
            [
                'title' => 'CQ Test',
                'created_at' => '2023-04-15 05:53:11',
                'updated_at' => '2023-04-15 05:53:11',
            ],
        ];

        DB::table('question_types')->insertOrIgnore($array);
    }
}
