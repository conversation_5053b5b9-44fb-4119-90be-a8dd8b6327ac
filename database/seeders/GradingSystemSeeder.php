<?php

namespace Database\Seeders;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class GradingSystemSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $gradingSystems = [
            [
                'letter_grade' => 'A+',
                'grade_poing' => '10',
                'min_percentage' => '90',
                'max_percentage' => '100',
                'remarks' => 'Excellent',
                'status' => 1,
                'created_at' => '2023-04-15 05:52:09',
                'updated_at' => '2023-04-15 05:52:09',
            ],
            [
                'letter_grade' => 'A',
                'grade_poing' => '9',
                'min_percentage' => '85',
                'max_percentage' => '89',
                'remarks' => 'Excellent',
                'status' => 1,
                'created_at' => '2023-04-15 05:52:36',
                'updated_at' => '2023-04-15 05:52:36',
            ],
            [
                'letter_grade' => 'A-',
                'grade_poing' => '8',
                'min_percentage' => '80',
                'max_percentage' => '84',
                'remarks' => 'Very Good',
                'status' => 1,
                'created_at' => '2023-04-15 05:53:11',
                'updated_at' => '2023-04-15 05:53:11',
            ],
            [
                'letter_grade' => 'B+',
                'grade_poing' => '7',
                'min_percentage' => '75',
                'max_percentage' => '79',
                'remarks' => 'Very Good',
                'status' => 1,
                'created_at' => '2023-04-15 05:53:42',
                'updated_at' => '2023-04-15 05:53:42',
            ],
            [
                'letter_grade' => 'B',
                'grade_poing' => '6',
                'min_percentage' => '70',
                'max_percentage' => '74',
                'remarks' => 'Very Good',
                'status' => 1,
                'created_at' => '2023-04-15 05:54:12',
                'updated_at' => '2023-04-15 05:54:12',
            ],
            [
                'letter_grade' => 'C+',
                'grade_poing' => '5',
                'min_percentage' => '65',
                'max_percentage' => '69',
                'remarks' => 'Good',
                'status' => 1,
                'created_at' => '2023-04-15 05:54:38',
                'updated_at' => '2023-04-15 05:54:38',
            ],
            [
                'letter_grade' => 'C',
                'grade_poing' => '4',
                'min_percentage' => '60',
                'max_percentage' => '64',
                'remarks' => 'Good',
                'status' => 1,
                'created_at' => '2023-04-15 05:55:05',
                'updated_at' => '2023-04-15 05:55:05',
            ],
            [
                'letter_grade' => 'D',
                'grade_poing' => '3',
                'min_percentage' => '55',
                'max_percentage' => '59',
                'remarks' => 'Possible',
                'status' => 1,
                'created_at' => '2023-04-15 05:55:50',
                'updated_at' => '2023-04-15 05:55:50',
            ],
            [
                'letter_grade' => 'E',
                'grade_poing' => '1',
                'min_percentage' => '40',
                'max_percentage' => '49',
                'remarks' => 'Possible',
                'status' => 1,
                'created_at' => '2023-04-15 05:56:17',
                'updated_at' => '2023-04-15 05:56:17',
            ],
            [
                'letter_grade' => 'F',
                'grade_poing' => '0',
                'min_percentage' => '0',
                'max_percentage' => '39',
                'remarks' => 'Failure',
                'status' => 1,
                'created_at' => '2023-04-15 05:56:53',
                'updated_at' => '2023-04-15 05:56:53',
            ],
            // Add the rest of the data entries here
        ];

        DB::table('grading_systems')->insert($gradingSystems);
    }
}
